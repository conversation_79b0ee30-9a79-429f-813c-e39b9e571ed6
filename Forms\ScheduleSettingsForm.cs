/*
 * ========================================
 * 文件名: ScheduleSettingsForm.cs
 * 功能描述: 定时任务设置配置窗体
 * ========================================
 *
 * 主要功能:
 * 1. 提供定时任务的完整配置界面
 * 2. 支持多种定时模式的设置和切换
 * 3. 包含高级定时选项的配置
 * 4. 提供直观的时间选择和验证
 * 5. 支持定时任务的启用和禁用控制
 *
 * 界面结构:
 * - 启用控制：定时处理总开关
 * - 模式选择：一次性、指定时间、定时循环三种模式
 * - 时间设置：根据选择的模式显示相应的时间配置面板
 * - 高级设置：运行次数限制、过期时间等高级选项
 * - 操作按钮：确定、取消操作
 *
 * 定时模式类型:
 *
 * 一次性启动模式:
 * - 设置具体的执行时间（年月日时分秒）
 * - 只执行一次，执行后自动禁用
 * - 适用于特定时间点的一次性任务
 *
 * 指定时间启动模式:
 * - 每年执行：设置具体的月日时分秒
 * - 每月执行：设置具体的日时分秒
 * - 每天执行：设置具体的时分秒
 * - 每小时执行：设置具体的分秒
 *
 * 定时循环模式:
 * - 设置开始时间和时间间隔
 * - 支持天、小时、分钟、秒的间隔设置
 * - 可以无限循环或设置次数限制
 *
 * 高级设置选项:
 * - 无限制运行：任务可以无限次执行
 * - 限制运行次数：设置最大执行次数
 * - 设置过期时间：任务在指定时间后自动停止
 * - 运行统计：显示已执行次数和下次执行时间
 *
 * 时间控件特性:
 * - DateTimePicker集成：提供友好的时间选择界面
 * - 自定义格式显示：根据需要显示不同的时间格式
 * - 数值范围验证：确保输入的时间间隔在有效范围内
 * - 实时预览：显示下次执行时间的计算结果
 *
 * 界面交互特性:
 * - 动态面板切换：根据选择的模式显示相应的设置面板
 * - 控件状态联动：启用状态影响所有子控件的可用性
 * - 输入验证：实时验证用户输入的有效性
 * - 错误提示：提供清晰的错误信息和修正建议
 *
 * 数据绑定:
 * - 与ScheduleSettings模型完全集成
 * - 支持设置的加载和保存
 * - 实时更新模型数据
 * - 提供数据验证和错误处理
 *
 * 布局特性:
 * - 响应式布局：支持窗体大小调整
 * - 分组显示：相关设置分组管理
 * - 滚动支持：内容过多时提供滚动功能
 * - 字体和颜色：统一的视觉风格
 *
 * 注意事项:
 * - 支持复杂的定时任务调度需求
 * - 包含完整的时间计算和验证逻辑
 * - 实现了用户友好的交互体验
 * - 提供了专业的定时任务管理功能
 */

using System;
using System.Drawing;
using System.Windows.Forms;
using PPTPiliangChuli.Models;

namespace PPTPiliangChuli.Forms
{
    public class ScheduleSettingsForm : Form
    {
        private ScheduleSettings scheduleSettings;

        // 添加公开属性
        public ScheduleSettings ScheduleSettings => scheduleSettings;

        // 定义控件
        private CheckBox? enabledCheckBox;
        private GroupBox? modeGroupBox;
        private RadioButton? oneTimeRadioButton;
        private RadioButton? specificTimeRadioButton;
        private RadioButton? intervalRadioButton;

        // 一次性启动面板
        private Panel? oneTimePanel;
        private DateTimePicker? oneTimeDateTimePicker;

        // 指定时间启动面板
        private Panel? specificTimePanel;
        private RadioButton? yearlyRadioButton;
        private RadioButton? monthlyRadioButton;
        private RadioButton? dailyRadioButton;
        private RadioButton? hourlyRadioButton;

        // 年度面板
        private Panel? yearlyPanel;
        private DateTimePicker? datePicker;
        private DateTimePicker? timePicker;

        // 月度面板
        private Panel? monthlyPanel;
        private DateTimePicker? monthDayPicker;
        private DateTimePicker? dayTimePicker;

        // 日度面板
        private Panel? dailyPanel;
        private DateTimePicker? hourMinSecPicker;

        // 小时面板
        private Panel? hourlyPanel;

        // 定时启动面板
        private Panel? intervalPanel;
        private DateTimePicker? startDateTimePicker;
        private NumericUpDown? daysNumericUpDown;
        private NumericUpDown? hoursNumericUpDown;
        private NumericUpDown? minutesNumericUpDown;
        private NumericUpDown? secondsNumericUpDown;

        // 高级设置面板
        private GroupBox? advancedGroupBox;
        private RadioButton? noLimitRadioButton;
        private RadioButton? limitRunsRadioButton;
        private NumericUpDown? maxRunsNumericUpDown;
        private RadioButton? expirationRadioButton;
        private DateTimePicker? expirationDateTimePicker;

        // 操作按钮
        private Button? okButton;
        private Button? cancelButton;

        public ScheduleSettingsForm(ScheduleSettings settings)
        {
            scheduleSettings = settings;
            InitializeComponent();
            LoadSettings();
        }

        private void InitializeComponent()
        {
            // 设置窗体属性 - 优化窗口尺寸以适应宽松布局
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ScheduleSettingsForm));
            this.Text = "定时启动设置";
            this.Size = new Size(950, 1100); // 进一步增大窗口高度
            this.MinimumSize = new Size(950, 1100);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.ShowIcon = true;
            this.Icon = (Icon?)resources.GetObject("$this.Icon");

            // 创建主滚动面板
            Panel mainScrollPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                Padding = new Padding(25)
            };

            // 创建主布局 - 使用固定高度确保所有内容可见
            TableLayoutPanel mainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Top,
                ColumnCount = 1,
                RowCount = 3,
                AutoSize = true,
                AutoSizeMode = AutoSizeMode.GrowAndShrink
            };

            mainLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));  // 顶部设置区域
            mainLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));  // 高级设置区域
            mainLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));  // 底部按钮区域

            // 创建顶部设置面板 - 增加行间距
            TableLayoutPanel topPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 5,
                AutoSize = true,
                Margin = new Padding(0, 0, 0, 25) // 增加底部间距
            };

            topPanel.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // 启用复选框
            topPanel.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // 模式选择组框
            topPanel.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // 一次性启动面板
            topPanel.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // 指定时间启动面板
            topPanel.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // 定时启动面板

            // 创建启用复选框 - 增大字体和间距
            enabledCheckBox = new CheckBox
            {
                Text = "启用定时处理",
                Checked = scheduleSettings.Enabled,
                Dock = DockStyle.Fill,
                AutoSize = true,
                Height = 50, // 设置最小行高
                Margin = new Padding(8, 18, 8, 25), // 增加边距
                Font = new Font(this.Font.FontFamily, 12F, FontStyle.Bold), // 增大字体
                TextAlign = ContentAlignment.MiddleLeft
            };
            enabledCheckBox.CheckedChanged += (s, e) => UpdateControlsState();
            topPanel.Controls.Add(enabledCheckBox, 0, 0);

            // 创建模式选择组框 - 增大字体和间距
            modeGroupBox = new GroupBox
            {
                Text = "选择模式",
                Dock = DockStyle.Fill,
                AutoSize = true,
                Height = 95, // 增大固定高度
                Padding = new Padding(20), // 增加内边距
                Margin = new Padding(8, 12, 8, 25), // 增加边距
                Font = new Font(this.Font.FontFamily, 12F, FontStyle.Bold) // 增大字体
            };

            TableLayoutPanel modeLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(0, 8, 0, 8) // 增加上下边距
            };

            modeLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33F));
            modeLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33F));
            modeLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33F));

            oneTimeRadioButton = new RadioButton
            {
                Text = "一次性启动",
                Checked = scheduleSettings.ScheduleMode == 0,
                Dock = DockStyle.Fill,
                AutoSize = true,
                Height = 50, // 增大最小行高
                Font = new Font(this.Font.FontFamily, 11F, FontStyle.Regular), // 增大字体
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(8, 10, 8, 10) // 增加边距
            };
            oneTimeRadioButton.CheckedChanged += (s, e) => UpdateDisplayPanels();

            specificTimeRadioButton = new RadioButton
            {
                Text = "指定时间启动",
                Checked = scheduleSettings.ScheduleMode == 1,
                Dock = DockStyle.Fill,
                AutoSize = true,
                Height = 50, // 增大最小行高
                Font = new Font(this.Font.FontFamily, 11F, FontStyle.Regular), // 增大字体
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(8, 10, 8, 10) // 增加边距
            };
            specificTimeRadioButton.CheckedChanged += (s, e) => UpdateDisplayPanels();

            intervalRadioButton = new RadioButton
            {
                Text = "定时启动",
                Checked = scheduleSettings.ScheduleMode == 2,
                Dock = DockStyle.Fill,
                AutoSize = true,
                Height = 50, // 增大最小行高
                Font = new Font(this.Font.FontFamily, 11F, FontStyle.Regular), // 增大字体
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(8, 10, 8, 10) // 增加边距
            };
            intervalRadioButton.CheckedChanged += (s, e) => UpdateDisplayPanels();

            modeLayout.Controls.Add(oneTimeRadioButton, 0, 0);
            modeLayout.Controls.Add(specificTimeRadioButton, 1, 0);
            modeLayout.Controls.Add(intervalRadioButton, 2, 0);

            modeGroupBox.Controls.Add(modeLayout);
            topPanel.Controls.Add(modeGroupBox, 0, 1);

            // 创建一次性启动面板 - 增大高度和间距
            oneTimePanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoSize = false,
                Height = 140, // 增大高度
                Padding = new Padding(12), // 增加内边距
                BorderStyle = BorderStyle.FixedSingle,
                Margin = new Padding(8, 12, 8, 25), // 增加边距
                Visible = scheduleSettings.ScheduleMode == 0
            };

            GroupBox oneTimeGroupBox = new GroupBox
            {
                Text = "一次性启动设置",
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(20), // 增加内边距
                Font = new Font(this.Font.FontFamily, 12F, FontStyle.Bold) // 增大字体
            };

            TableLayoutPanel oneTimeLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(0, 12, 0, 12) // 增加上下边距
            };

            oneTimeLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30)); // 调整比例
            oneTimeLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 70));

            Label oneTimeLabel = new Label
            {
                Text = "执行时间:",
                Dock = DockStyle.Fill,
                AutoSize = true,
                Height = 50, // 增大最小行高
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font(this.Font.FontFamily, 11F, FontStyle.Regular), // 增大字体
                Margin = new Padding(8, 10, 12, 10) // 增加边距
            };

            oneTimeDateTimePicker = new DateTimePicker
            {
                Format = DateTimePickerFormat.Custom,
                CustomFormat = "yyyy-MM-dd HH:mm:ss",
                Value = scheduleSettings.OneTimeRunTime,
                Dock = DockStyle.Fill,
                Height = 50, // 增大最小行高
                Font = new Font(this.Font.FontFamily, 11F, FontStyle.Regular), // 增大字体
                Margin = new Padding(8, 10, 8, 10) // 增加边距
            };

            // 设置DateTimePicker文字居中
            oneTimeDateTimePicker.Paint += (s, e) => {
                var picker = s as DateTimePicker;
                if (picker != null)
                {
                    e.Graphics.FillRectangle(new SolidBrush(picker.BackColor), picker.ClientRectangle);
                    var textRect = picker.ClientRectangle;
                    textRect.X += 3;
                    textRect.Width -= 20; // 为下拉箭头留空间
                    TextRenderer.DrawText(e.Graphics, picker.Text, picker.Font, textRect,
                        picker.ForeColor, TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                }
            };

            oneTimeLayout.Controls.Add(oneTimeLabel, 0, 0);
            oneTimeLayout.Controls.Add(oneTimeDateTimePicker, 1, 0);

            oneTimeGroupBox.Controls.Add(oneTimeLayout);
            oneTimePanel.Controls.Add(oneTimeGroupBox);
            topPanel.Controls.Add(oneTimePanel, 0, 2);

            // 创建指定时间启动面板
            specificTimePanel = CreateSpecificTimePanel();
            topPanel.Controls.Add(specificTimePanel, 0, 3);

            // 创建定时启动面板
            intervalPanel = CreateIntervalPanel();
            topPanel.Controls.Add(intervalPanel, 0, 4);

            // 创建高级设置面板
            Panel advancedPanel = CreateAdvancedPanel();

            // 创建底部按钮面板 - 增大按钮尺寸和间距
            TableLayoutPanel buttonPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1,
                AutoSize = true,
                Height = 70, // 增大固定高度
                Margin = new Padding(8, 25, 8, 18) // 增加边距
            };

            buttonPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100)); // 弹性空间
            buttonPanel.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));     // 确定按钮
            buttonPanel.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));     // 取消按钮

            okButton = new Button
            {
                Text = "确定",
                Size = new Size(120, 50), // 增大按钮尺寸
                Margin = new Padding(8, 10, 18, 10), // 增加边距
                Font = new Font(this.Font.FontFamily, 11F, FontStyle.Regular), // 增大字体
                Anchor = AnchorStyles.Right,
                UseVisualStyleBackColor = true
            };
            okButton.Click += OkButton_Click;

            cancelButton = new Button
            {
                Text = "取消",
                Size = new Size(120, 50), // 增大按钮尺寸
                Margin = new Padding(8, 10, 8, 10), // 增加边距
                Font = new Font(this.Font.FontFamily, 11F, FontStyle.Regular), // 增大字体
                Anchor = AnchorStyles.Right,
                UseVisualStyleBackColor = true
            };
            cancelButton.Click += (s, e) => this.DialogResult = DialogResult.Cancel;

            Panel spacerPanel = new Panel
            {
                Dock = DockStyle.Fill
            };

            buttonPanel.Controls.Add(spacerPanel, 0, 0);
            buttonPanel.Controls.Add(okButton, 1, 0);
            buttonPanel.Controls.Add(cancelButton, 2, 0);

            // 添加面板到主布局
            mainLayout.Controls.Add(topPanel, 0, 0);
            mainLayout.Controls.Add(advancedPanel, 0, 1);
            mainLayout.Controls.Add(buttonPanel, 0, 2);

            // 将主布局添加到滚动面板
            mainScrollPanel.Controls.Add(mainLayout);

            // 添加滚动面板到窗体
            this.Controls.Add(mainScrollPanel);

            // 更新控件状态
            UpdateControlsState();
        }

        // 创建指定时间启动面板
        private Panel CreateSpecificTimePanel()
        {
            Panel panel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoSize = false,
                Height = 280, // 增大高度
                Padding = new Padding(12), // 增加内边距
                BorderStyle = BorderStyle.FixedSingle,
                Margin = new Padding(8, 12, 8, 25), // 增加边距
                Visible = scheduleSettings.ScheduleMode == 1
            };

            GroupBox groupBox = new GroupBox
            {
                Text = "指定时间启动设置",
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(20), // 增加内边距
                Font = new Font(this.Font.FontFamily, 12F, FontStyle.Bold) // 增大字体
            };

            TableLayoutPanel layout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 6,
                AutoSize = true,
                Padding = new Padding(0),
                Margin = new Padding(0),
                AutoScroll = true
            };

            layout.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // 频率单选按钮
            layout.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // 年度面板
            layout.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // 月度面板
            layout.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // 日度面板
            layout.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // 小时面板
            layout.RowStyles.Add(new RowStyle(SizeType.Percent, 100)); // 空白填充

            // 创建频率单选按钮面板
            TableLayoutPanel radioPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 4,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(0, 8, 0, 15) // 增加边距
            };

            radioPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 25));
            radioPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 25));
            radioPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 25));
            radioPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 25));

            yearlyRadioButton = new RadioButton
            {
                Text = "每年",
                Checked = scheduleSettings.RunFrequency == 0,
                Dock = DockStyle.Fill,
                AutoSize = true,
                Height = 45, // 增大行高
                Font = new Font(this.Font.FontFamily, 11F, FontStyle.Regular), // 增大字体
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(8, 8, 8, 8) // 增加边距
            };
            yearlyRadioButton.CheckedChanged += (s, e) => UpdateSpecificTimePanels();

            monthlyRadioButton = new RadioButton
            {
                Text = "每月",
                Checked = scheduleSettings.RunFrequency == 1,
                Dock = DockStyle.Fill,
                AutoSize = true,
                Height = 45, // 增大行高
                Font = new Font(this.Font.FontFamily, 11F, FontStyle.Regular), // 增大字体
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(8, 8, 8, 8) // 增加边距
            };
            monthlyRadioButton.CheckedChanged += (s, e) => UpdateSpecificTimePanels();

            dailyRadioButton = new RadioButton
            {
                Text = "每天",
                Checked = scheduleSettings.RunFrequency == 2,
                Dock = DockStyle.Fill,
                AutoSize = true,
                Height = 45, // 增大行高
                Font = new Font(this.Font.FontFamily, 11F, FontStyle.Regular), // 增大字体
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(8, 8, 8, 8) // 增加边距
            };
            dailyRadioButton.CheckedChanged += (s, e) => UpdateSpecificTimePanels();

            hourlyRadioButton = new RadioButton
            {
                Text = "每小时",
                Checked = scheduleSettings.RunFrequency == 3,
                Dock = DockStyle.Fill,
                AutoSize = true,
                Height = 45, // 增大行高
                Font = new Font(this.Font.FontFamily, 11F, FontStyle.Regular), // 增大字体
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(8, 8, 8, 8) // 增加边距
            };
            hourlyRadioButton.CheckedChanged += (s, e) => UpdateSpecificTimePanels();

            radioPanel.Controls.Add(yearlyRadioButton, 0, 0);
            radioPanel.Controls.Add(monthlyRadioButton, 1, 0);
            radioPanel.Controls.Add(dailyRadioButton, 2, 0);
            radioPanel.Controls.Add(hourlyRadioButton, 3, 0);

            layout.Controls.Add(radioPanel, 0, 0);

            // 创建年度面板
            yearlyPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoSize = true,
                BorderStyle = BorderStyle.FixedSingle,
                Margin = new Padding(5, 0, 5, 5),
                Visible = scheduleSettings.RunFrequency == 0
            };

            GroupBox yearlyGroupBox = new GroupBox
            {
                Text = "年度设置",
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(5),
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Regular),
                Enabled = true
            };

            TableLayoutPanel yearlyLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 2,
                AutoSize = true
            };

            yearlyLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 40));
            yearlyLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 60));

            Label dateLabel = new Label
            {
                Text = "日期:",
                Dock = DockStyle.Fill,
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Regular)
            };

            datePicker = new DateTimePicker
            {
                Format = DateTimePickerFormat.Custom,
                CustomFormat = "MM月dd日",
                Value = new DateTime(DateTime.Now.Year, scheduleSettings.Month, scheduleSettings.Day),
                Dock = DockStyle.Fill,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Regular)
            };

            // 设置DateTimePicker文字居中
            datePicker.Paint += (s, e) => {
                if (s is DateTimePicker picker)
                {
                    e.Graphics.FillRectangle(new SolidBrush(picker.BackColor), picker.ClientRectangle);
                    var textRect = picker.ClientRectangle;
                    textRect.X += 3;
                    textRect.Width -= 20; // 为下拉箭头留空间
                    TextRenderer.DrawText(e.Graphics, picker.Text, picker.Font, textRect,
                        picker.ForeColor, TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                }
            };

            Label timeLabel = new Label
            {
                Text = "时间:",
                Dock = DockStyle.Fill,
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Regular)
            };

            timePicker = new DateTimePicker
            {
                Format = DateTimePickerFormat.Custom,
                CustomFormat = "HH:mm:ss",
                Value = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day,
                                     scheduleSettings.Hour, scheduleSettings.Minute, scheduleSettings.Second),
                Dock = DockStyle.Fill,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Regular),
                ShowUpDown = true
            };

            // 设置DateTimePicker文字居中
            timePicker.Paint += (s, e) => {
                if (s is DateTimePicker picker)
                {
                    e.Graphics.FillRectangle(new SolidBrush(picker.BackColor), picker.ClientRectangle);
                    var textRect = picker.ClientRectangle;
                    textRect.X += 3;
                    textRect.Width -= 20; // 为上下箭头留空间
                    TextRenderer.DrawText(e.Graphics, picker.Text, picker.Font, textRect,
                        picker.ForeColor, TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                }
            };

            yearlyLayout.Controls.Add(dateLabel, 0, 0);
            yearlyLayout.Controls.Add(datePicker, 1, 0);
            yearlyLayout.Controls.Add(timeLabel, 0, 1);
            yearlyLayout.Controls.Add(timePicker, 1, 1);

            yearlyGroupBox.Controls.Add(yearlyLayout);
            yearlyPanel.Controls.Add(yearlyGroupBox);

            layout.Controls.Add(yearlyPanel, 0, 1);

            // 创建月度面板
            monthlyPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoSize = true,
                BorderStyle = BorderStyle.FixedSingle,
                Margin = new Padding(5, 0, 5, 5),
                Visible = scheduleSettings.RunFrequency == 1
            };

            GroupBox monthlyGroupBox = new GroupBox
            {
                Text = "月度设置",
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(5),
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Regular)
            };

            TableLayoutPanel monthlyLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 2,
                AutoSize = true
            };

            monthlyLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 40));
            monthlyLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 60));

            Label dayLabel = new Label
            {
                Text = "日期:",
                Dock = DockStyle.Fill,
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Regular)
            };

            monthDayPicker = new DateTimePicker
            {
                Format = DateTimePickerFormat.Custom,
                CustomFormat = "dd日",
                Value = new DateTime(DateTime.Now.Year, DateTime.Now.Month, scheduleSettings.Day),
                Dock = DockStyle.Fill,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Regular)
            };

            // 设置DateTimePicker文字居中
            monthDayPicker.Paint += (s, e) => {
                if (s is DateTimePicker picker)
                {
                    e.Graphics.FillRectangle(new SolidBrush(picker.BackColor), picker.ClientRectangle);
                    var textRect = picker.ClientRectangle;
                    textRect.X += 3;
                    textRect.Width -= 20; // 为下拉箭头留空间
                    TextRenderer.DrawText(e.Graphics, picker.Text, picker.Font, textRect,
                        picker.ForeColor, TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                }
            };

            Label dayTimeLabel = new Label
            {
                Text = "时间:",
                Dock = DockStyle.Fill,
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Regular)
            };

            dayTimePicker = new DateTimePicker
            {
                Format = DateTimePickerFormat.Custom,
                CustomFormat = "HH:mm:ss",
                Value = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day,
                                     scheduleSettings.Hour, scheduleSettings.Minute, scheduleSettings.Second),
                Dock = DockStyle.Fill,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Regular),
                ShowUpDown = true
            };

            // 设置DateTimePicker文字居中
            dayTimePicker.Paint += (s, e) => {
                if (s is DateTimePicker picker)
                {
                    e.Graphics.FillRectangle(new SolidBrush(picker.BackColor), picker.ClientRectangle);
                    var textRect = picker.ClientRectangle;
                    textRect.X += 3;
                    textRect.Width -= 20; // 为上下箭头留空间
                    TextRenderer.DrawText(e.Graphics, picker.Text, picker.Font, textRect,
                        picker.ForeColor, TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                }
            };

            monthlyLayout.Controls.Add(dayLabel, 0, 0);
            monthlyLayout.Controls.Add(monthDayPicker, 1, 0);
            monthlyLayout.Controls.Add(dayTimeLabel, 0, 1);
            monthlyLayout.Controls.Add(dayTimePicker, 1, 1);

            monthlyGroupBox.Controls.Add(monthlyLayout);
            monthlyPanel.Controls.Add(monthlyGroupBox);

            layout.Controls.Add(monthlyPanel, 0, 2);

            // 创建日度面板
            dailyPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoSize = true,
                BorderStyle = BorderStyle.FixedSingle,
                Margin = new Padding(5, 0, 5, 5),
                Visible = scheduleSettings.RunFrequency == 2
            };

            GroupBox dailyGroupBox = new GroupBox
            {
                Text = "日度设置",
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(5),
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Regular),
                Enabled = true
            };

            TableLayoutPanel dailyLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 1,
                AutoSize = true
            };

            dailyLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 40));
            dailyLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 60));

            Label hourTimeLabel = new Label
            {
                Text = "时间:",
                Dock = DockStyle.Fill,
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Regular)
            };

            hourMinSecPicker = new DateTimePicker
            {
                Format = DateTimePickerFormat.Custom,
                CustomFormat = "HH:mm:ss",
                Value = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day,
                                     scheduleSettings.Hour, scheduleSettings.Minute, scheduleSettings.Second),
                Dock = DockStyle.Fill,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Regular),
                ShowUpDown = true
            };

            // 设置DateTimePicker文字居中
            hourMinSecPicker.Paint += (s, e) => {
                if (s is DateTimePicker picker)
                {
                    e.Graphics.FillRectangle(new SolidBrush(picker.BackColor), picker.ClientRectangle);
                    var textRect = picker.ClientRectangle;
                    textRect.X += 3;
                    textRect.Width -= 20; // 为上下箭头留空间
                    TextRenderer.DrawText(e.Graphics, picker.Text, picker.Font, textRect,
                        picker.ForeColor, TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                }
            };

            dailyLayout.Controls.Add(hourTimeLabel, 0, 0);
            dailyLayout.Controls.Add(hourMinSecPicker, 1, 0);

            dailyGroupBox.Controls.Add(dailyLayout);
            dailyPanel.Controls.Add(dailyGroupBox);

            layout.Controls.Add(dailyPanel, 0, 3);

            // 创建小时面板
            hourlyPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoSize = true,
                BorderStyle = BorderStyle.FixedSingle,
                Margin = new Padding(5, 0, 5, 5),
                Visible = scheduleSettings.RunFrequency == 3
            };

            GroupBox hourlyGroupBox = new GroupBox
            {
                Text = "小时设置",
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(5),
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Regular)
            };

            TableLayoutPanel hourlyLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 1,
                AutoSize = true
            };

            hourlyLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 40));
            hourlyLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 60));

            Label minuteSecondLabel = new Label
            {
                Text = "分秒:",
                Dock = DockStyle.Fill,
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Regular)
            };

            DateTimePicker minuteSecondPicker = new DateTimePicker
            {
                Format = DateTimePickerFormat.Custom,
                CustomFormat = "mm:ss",
                Value = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day,
                                     0, scheduleSettings.Minute, scheduleSettings.Second),
                Dock = DockStyle.Fill,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Regular),
                ShowUpDown = true,
                Tag = "hourlyPicker"
            };

            // 设置DateTimePicker文字居中
            minuteSecondPicker.Paint += (s, e) => {
                if (s is DateTimePicker picker)
                {
                    e.Graphics.FillRectangle(new SolidBrush(picker.BackColor), picker.ClientRectangle);
                    var textRect = picker.ClientRectangle;
                    textRect.X += 3;
                    textRect.Width -= 20; // 为上下箭头留空间
                    TextRenderer.DrawText(e.Graphics, picker.Text, picker.Font, textRect,
                        picker.ForeColor, TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                }
            };

            hourlyLayout.Controls.Add(minuteSecondLabel, 0, 0);
            hourlyLayout.Controls.Add(minuteSecondPicker, 1, 0);

            hourlyGroupBox.Controls.Add(hourlyLayout);
            hourlyPanel.Controls.Add(hourlyGroupBox);

            layout.Controls.Add(hourlyPanel, 0, 4);

            // 添加空白填充
            layout.Controls.Add(new Panel(), 0, 5);

            groupBox.Controls.Add(layout);
            panel.Controls.Add(groupBox);

            return panel;
        }

        // 创建定时启动面板
        private Panel CreateIntervalPanel()
        {
            Panel panel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoSize = false,
                Height = 160, // 增大高度
                Padding = new Padding(12), // 增加内边距
                BorderStyle = BorderStyle.FixedSingle,
                Margin = new Padding(8, 12, 8, 25), // 增加边距
                Visible = scheduleSettings.ScheduleMode == 2
            };

            GroupBox groupBox = new GroupBox
            {
                Text = "定时启动设置",
                Dock = DockStyle.Fill,
                AutoSize = false,
                Padding = new Padding(20), // 增加内边距
                Font = new Font(this.Font.FontFamily, 12F, FontStyle.Bold), // 增大字体
                Margin = new Padding(0, 8, 0, 8) // 调整边距
            };

            TableLayoutPanel layout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2, // 两行：起始时间和间隔时间
                AutoSize = false,
                AutoScroll = false // 禁用滚动
            };

            layout.RowStyles.Add(new RowStyle(SizeType.AutoSize));  // 起始时间
            layout.RowStyles.Add(new RowStyle(SizeType.AutoSize));  // 间隔时间设置

            // 起始时间设置
            TableLayoutPanel startTimePanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(0, 8, 0, 15) // 增加边距
            };

            startTimePanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 25));
            startTimePanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 75));

            Label startTimeLabel = new Label
            {
                Text = "起始时间:",
                Dock = DockStyle.Fill,
                AutoSize = true,
                Height = 50, // 增大行高
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font(this.Font.FontFamily, 11F, FontStyle.Regular), // 增大字体
                Margin = new Padding(8, 10, 12, 10) // 增加边距
            };

            startDateTimePicker = new DateTimePicker
            {
                Format = DateTimePickerFormat.Custom,
                CustomFormat = "yyyy-MM-dd HH:mm:ss",
                Value = scheduleSettings.StartTime,
                Dock = DockStyle.Fill,
                Height = 50, // 增大行高
                Font = new Font(this.Font.FontFamily, 11F, FontStyle.Regular), // 增大字体
                Margin = new Padding(8, 10, 8, 10) // 增加边距
            };

            // 设置DateTimePicker文字居中
            startDateTimePicker.Paint += (s, e) => {
                if (s is DateTimePicker picker)
                {
                    e.Graphics.FillRectangle(new SolidBrush(picker.BackColor), picker.ClientRectangle);
                    var textRect = picker.ClientRectangle;
                    textRect.X += 3;
                    textRect.Width -= 20; // 为下拉箭头留空间
                    TextRenderer.DrawText(e.Graphics, picker.Text, picker.Font, textRect,
                        picker.ForeColor, TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                }
            };

            startTimePanel.Controls.Add(startTimeLabel, 0, 0);
            startTimePanel.Controls.Add(startDateTimePicker, 1, 0);

            layout.Controls.Add(startTimePanel, 0, 0);

            // 间隔时间设置 - 改为垂直布局的文字+输入框
            FlowLayoutPanel intervalPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.LeftToRight,
                WrapContents = true,
                AutoSize = true,
                Margin = new Padding(0, 0, 0, 5)
            };

            Label intervalLabel = new Label
            {
                Text = "间隔时间:",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Regular),
                Margin = new Padding(0, 4, 5, 0)
            };
            intervalPanel.Controls.Add(intervalLabel);

            // 天数输入
            daysNumericUpDown = new NumericUpDown
            {
                Minimum = 0,
                Maximum = 365,
                Value = scheduleSettings.IntervalDays,
                Width = 60,
                Height = 24,
                TextAlign = HorizontalAlignment.Center,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Regular),
                BackColor = (scheduleSettings.IntervalDays > 0 ||
                            scheduleSettings.IntervalHours > 0 ||
                            scheduleSettings.IntervalMinutes > 0 ||
                            scheduleSettings.IntervalSeconds > 0) ?
                            Color.White : Color.MistyRose
            };
            intervalPanel.Controls.Add(daysNumericUpDown);

            Label daysLabel = new Label
            {
                Text = "天",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Regular),
                Margin = new Padding(1, 4, 8, 0)
            };
            intervalPanel.Controls.Add(daysLabel);

            // 小时输入
            hoursNumericUpDown = new NumericUpDown
            {
                Minimum = 0,
                Maximum = 23,
                Value = scheduleSettings.IntervalHours,
                Width = 60,
                Height = 24,
                TextAlign = HorizontalAlignment.Center,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Regular)
            };
            intervalPanel.Controls.Add(hoursNumericUpDown);

            Label hoursLabel = new Label
            {
                Text = "小时",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Regular),
                Margin = new Padding(1, 4, 8, 0)
            };
            intervalPanel.Controls.Add(hoursLabel);

            // 分钟输入
            minutesNumericUpDown = new NumericUpDown
            {
                Minimum = 0,
                Maximum = 59,
                Value = scheduleSettings.IntervalMinutes,
                Width = 60,
                Height = 24,
                TextAlign = HorizontalAlignment.Center,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Regular)
            };
            intervalPanel.Controls.Add(minutesNumericUpDown);

            Label minutesLabel = new Label
            {
                Text = "分钟",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Regular),
                Margin = new Padding(1, 4, 8, 0)
            };
            intervalPanel.Controls.Add(minutesLabel);

            // 秒输入
            secondsNumericUpDown = new NumericUpDown
            {
                Minimum = 0,
                Maximum = 59,
                Value = scheduleSettings.IntervalSeconds,
                Width = 60,
                Height = 24,
                TextAlign = HorizontalAlignment.Center,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Regular)
            };
            intervalPanel.Controls.Add(secondsNumericUpDown);

            Label secondsLabel = new Label
            {
                Text = "秒",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Regular),
                Margin = new Padding(1, 4, 0, 0)
            };
            intervalPanel.Controls.Add(secondsLabel);

            // 添加间隔时间零值提示
            Label tipLabel = new Label
            {
                Text = "注意: 间隔时间至少需要设置1秒",
                AutoSize = true,
                ForeColor = Color.DarkRed,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Regular),
                Margin = new Padding(intervalLabel.Width + 5, 5, 0, 0)
            };
            intervalPanel.Controls.Add(tipLabel);

            // 添加ValueChanged事件来更新背景色
            EventHandler updateBgColor = (s, e) => {
                bool hasValue = (daysNumericUpDown.Value > 0 ||
                               hoursNumericUpDown.Value > 0 ||
                               minutesNumericUpDown.Value > 0 ||
                               secondsNumericUpDown.Value > 0);

                daysNumericUpDown.BackColor = hasValue ? Color.White : Color.MistyRose;
                tipLabel.Visible = !hasValue;
            };

            daysNumericUpDown.ValueChanged += updateBgColor;
            hoursNumericUpDown.ValueChanged += updateBgColor;
            minutesNumericUpDown.ValueChanged += updateBgColor;
            secondsNumericUpDown.ValueChanged += updateBgColor;

            layout.Controls.Add(intervalPanel, 0, 1);

            groupBox.Controls.Add(layout);
            panel.Controls.Add(groupBox);

            // 初始化提示标签可见性
            tipLabel.Visible = !(scheduleSettings.IntervalDays > 0 ||
                               scheduleSettings.IntervalHours > 0 ||
                               scheduleSettings.IntervalMinutes > 0 ||
                               scheduleSettings.IntervalSeconds > 0);

            return panel;
        }

        // 创建高级设置面板
        private Panel CreateAdvancedPanel()
        {
            advancedGroupBox = new GroupBox
            {
                Text = "高级设置（适用于指定时间启动和定时启动）",
                Dock = DockStyle.Top, // 改为Top，让内容自然展开
                AutoSize = true, // 让内容自动调整大小
                AutoSizeMode = AutoSizeMode.GrowAndShrink, // 根据内容自动调整
                Padding = new Padding(20), // 增加内边距
                Font = new Font(this.Font.FontFamily, 12F, FontStyle.Bold), // 增大字体
                Margin = new Padding(8, 15, 8, 15) // 增加边距
            };

            TableLayoutPanel layout = new TableLayoutPanel
            {
                Dock = DockStyle.Top, // 改为Top，让内容自然展开
                ColumnCount = 1,
                RowCount = 4,
                AutoSize = true,
                AutoSizeMode = AutoSizeMode.GrowAndShrink // 根据内容自动调整
            };

            layout.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // 描述
            layout.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // 无限制
            layout.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // 运行次数限制
            layout.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // 过期时间限制

            // 描述标签
            Label descLabel = new Label
            {
                Text = "设置循环启动的限制条件，可以限制运行次数或设置过期时间。",
                Dock = DockStyle.Fill,
                AutoSize = true,
                Font = new Font(this.Font.FontFamily, 11F, FontStyle.Regular), // 增大字体
                Margin = new Padding(0, 8, 0, 15) // 增加边距
            };
            layout.Controls.Add(descLabel, 0, 0);

            // 无限制选项
            noLimitRadioButton = new RadioButton
            {
                Text = "无限制",
                Checked = !scheduleSettings.UseLimitedRuns && !scheduleSettings.UseExpirationTime,
                Dock = DockStyle.Fill,
                AutoSize = true,
                Height = 45, // 增大行高
                Font = new Font(this.Font.FontFamily, 11F, FontStyle.Regular), // 增大字体
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(8, 8, 8, 12) // 增加边距
            };
            noLimitRadioButton.CheckedChanged += (s, e) => UpdateAdvancedControlsState();
            layout.Controls.Add(noLimitRadioButton, 0, 1);

            // 运行次数限制选项
            TableLayoutPanel countPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(0, 8, 0, 12) // 增加边距
            };

            countPanel.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            countPanel.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            countPanel.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));

            limitRunsRadioButton = new RadioButton
            {
                Text = "运行次数：",
                Checked = scheduleSettings.UseLimitedRuns,
                AutoSize = true,
                Height = 45, // 增大行高
                Font = new Font(this.Font.FontFamily, 11F, FontStyle.Regular), // 增大字体
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(8, 10, 12, 10) // 增加边距
            };
            limitRunsRadioButton.CheckedChanged += (s, e) => UpdateAdvancedControlsState();

            maxRunsNumericUpDown = new NumericUpDown
            {
                Minimum = 1,
                Maximum = 9999,
                Value = scheduleSettings.MaxRunCount > 0 ? scheduleSettings.MaxRunCount : 1,
                Width = 100, // 增大宽度
                Height = 45, // 增大高度
                TextAlign = HorizontalAlignment.Center,
                Font = new Font(this.Font.FontFamily, 11F, FontStyle.Regular), // 增大字体
                Enabled = scheduleSettings.UseLimitedRuns,
                Margin = new Padding(8, 10, 8, 10) // 增加边距
            };

            Label countUnitLabel = new Label
            {
                Text = "次",
                AutoSize = true,
                Height = 45, // 增大行高
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font(this.Font.FontFamily, 11F, FontStyle.Regular), // 增大字体
                Margin = new Padding(8, 10, 0, 10) // 增加边距
            };

            countPanel.Controls.Add(limitRunsRadioButton, 0, 0);
            countPanel.Controls.Add(maxRunsNumericUpDown, 1, 0);
            countPanel.Controls.Add(countUnitLabel, 2, 0);

            layout.Controls.Add(countPanel, 0, 2);

            // 过期时间限制选项
            TableLayoutPanel timePanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(0, 8, 0, 12) // 增加边距
            };

            timePanel.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            timePanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            expirationRadioButton = new RadioButton
            {
                Text = "过期时间：",
                Checked = scheduleSettings.UseExpirationTime,
                AutoSize = true,
                Height = 45, // 增大行高
                Font = new Font(this.Font.FontFamily, 11F, FontStyle.Regular), // 增大字体
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(8, 10, 12, 10) // 增加边距
            };
            expirationRadioButton.CheckedChanged += (s, e) => UpdateAdvancedControlsState();

            expirationDateTimePicker = new DateTimePicker
            {
                Format = DateTimePickerFormat.Custom,
                CustomFormat = "yyyy-MM-dd HH:mm:ss",
                Value = scheduleSettings.ExpirationTime,
                Dock = DockStyle.Fill,
                Height = 45, // 增大高度
                Font = new Font(this.Font.FontFamily, 11F, FontStyle.Regular), // 增大字体
                Enabled = scheduleSettings.UseExpirationTime,
                Margin = new Padding(8, 10, 8, 10) // 增加边距
            };

            // 设置DateTimePicker文字居中
            expirationDateTimePicker.Paint += (s, e) => {
                if (s is DateTimePicker picker)
                {
                    e.Graphics.FillRectangle(new SolidBrush(picker.BackColor), picker.ClientRectangle);
                    var textRect = picker.ClientRectangle;
                    textRect.X += 3;
                    textRect.Width -= 20; // 为下拉箭头留空间
                    TextRenderer.DrawText(e.Graphics, picker.Text, picker.Font, textRect,
                        picker.ForeColor, TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                }
            };

            timePanel.Controls.Add(expirationRadioButton, 0, 0);
            timePanel.Controls.Add(expirationDateTimePicker, 1, 0);

            layout.Controls.Add(timePanel, 0, 3);

            advancedGroupBox.Controls.Add(layout);

            // 创建容器面板包装GroupBox
            Panel containerPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoSize = true, // 改为true让内容自动调整大小
                Padding = new Padding(0)
            };
            containerPanel.Controls.Add(advancedGroupBox);

            return containerPanel;
        }

        // 标志位，防止RadioButton事件处理时的无限递归
        private bool _isUpdatingAdvancedControls = false;

        // 更新高级设置控件状态
        private void UpdateAdvancedControlsState()
        {
            // 防止递归调用
            if (_isUpdatingAdvancedControls)
                return;

            _isUpdatingAdvancedControls = true;

            try
            {
                // 处理高级设置RadioButton的互斥逻辑
                // 确保三个选项中只有一个被选中
                if (noLimitRadioButton != null && limitRunsRadioButton != null && expirationRadioButton != null)
                {
                    // 检查当前状态
                    bool noLimitChecked = noLimitRadioButton.Checked;
                    bool limitRunsChecked = limitRunsRadioButton.Checked;
                    bool expirationChecked = expirationRadioButton.Checked;

                    // 计算选中的数量
                    int checkedCount = (noLimitChecked ? 1 : 0) + (limitRunsChecked ? 1 : 0) + (expirationChecked ? 1 : 0);

                    // 如果有多个被选中，需要处理互斥逻辑
                    if (checkedCount > 1)
                    {
                        // 根据优先级确定应该保留哪个选项
                        // 优先级：过期时间 > 运行次数 > 无限制
                        if (expirationChecked)
                        {
                            noLimitRadioButton.Checked = false;
                            limitRunsRadioButton.Checked = false;
                        }
                        else if (limitRunsChecked)
                        {
                            noLimitRadioButton.Checked = false;
                            expirationRadioButton.Checked = false;
                        }
                        else if (noLimitChecked)
                        {
                            limitRunsRadioButton.Checked = false;
                            expirationRadioButton.Checked = false;
                        }
                    }
                    // 如果没有任何RadioButton被选中，默认选中"无限制"
                    else if (checkedCount == 0)
                    {
                        noLimitRadioButton.Checked = true;
                    }
                }

                // 更新相关控件的启用状态
                if (maxRunsNumericUpDown != null)
                    maxRunsNumericUpDown.Enabled = limitRunsRadioButton?.Checked == true;

                if (expirationDateTimePicker != null)
                    expirationDateTimePicker.Enabled = expirationRadioButton?.Checked == true;
            }
            finally
            {
                _isUpdatingAdvancedControls = false;
            }
        }

        // 更新控件显示状态
        private void UpdateDisplayPanels()
        {
            if (oneTimePanel != null)
                oneTimePanel.Visible = oneTimeRadioButton?.Checked == true;

            if (specificTimePanel != null)
                specificTimePanel.Visible = specificTimeRadioButton?.Checked == true;

            if (intervalPanel != null)
                intervalPanel.Visible = intervalRadioButton?.Checked == true;

            if (specificTimeRadioButton?.Checked == true)
            {
                UpdateSpecificTimePanels();
            }

            UpdateControlsState();
        }

        // 更新指定时间面板显示状态
        private void UpdateSpecificTimePanels()
        {
            bool specificEnabled = enabledCheckBox?.Checked == true && specificTimeRadioButton?.Checked == true;

            if (yearlyPanel != null)
            {
                yearlyPanel.Visible = yearlyRadioButton?.Checked == true;
                yearlyPanel.Enabled = specificEnabled && yearlyRadioButton?.Checked == true;
            }

            if (monthlyPanel != null)
            {
                monthlyPanel.Visible = monthlyRadioButton?.Checked == true;
                monthlyPanel.Enabled = specificEnabled && monthlyRadioButton?.Checked == true;
            }

            if (dailyPanel != null)
            {
                dailyPanel.Visible = dailyRadioButton?.Checked == true;
                dailyPanel.Enabled = specificEnabled && dailyRadioButton?.Checked == true;
            }

            if (hourlyPanel != null)
            {
                hourlyPanel.Visible = hourlyRadioButton?.Checked == true;
                hourlyPanel.Enabled = specificEnabled && hourlyRadioButton?.Checked == true;
            }

            // 更新控件启用状态
            if (datePicker != null)
                datePicker.Enabled = specificEnabled && yearlyRadioButton?.Checked == true;

            if (timePicker != null)
                timePicker.Enabled = specificEnabled && yearlyRadioButton?.Checked == true;

            if (monthDayPicker != null)
                monthDayPicker.Enabled = specificEnabled && monthlyRadioButton?.Checked == true;

            if (dayTimePicker != null)
                dayTimePicker.Enabled = specificEnabled && monthlyRadioButton?.Checked == true;

            if (hourMinSecPicker != null)
                hourMinSecPicker.Enabled = specificEnabled && dailyRadioButton?.Checked == true;
        }

        // 加载设置
        private void LoadSettings()
        {
            if (enabledCheckBox != null)
                enabledCheckBox.Checked = scheduleSettings.Enabled;

            // 设置模式单选按钮
            if (oneTimeRadioButton != null)
                oneTimeRadioButton.Checked = scheduleSettings.ScheduleMode == 0;

            if (specificTimeRadioButton != null)
                specificTimeRadioButton.Checked = scheduleSettings.ScheduleMode == 1;

            if (intervalRadioButton != null)
                intervalRadioButton.Checked = scheduleSettings.ScheduleMode == 2;

            // 处理兼容性 - 如果RunFrequency未设置但旧属性已设置，则根据旧设置推断RunFrequency
            if (scheduleSettings.RunFrequency == 0 && (scheduleSettings.RunYearly || scheduleSettings.RunMonthly ||
                scheduleSettings.RunDaily || scheduleSettings.RunHourly))
            {
                // 按优先级排序确定频率
                if (scheduleSettings.RunYearly)
                    scheduleSettings.RunFrequency = 0;
                else if (scheduleSettings.RunMonthly)
                    scheduleSettings.RunFrequency = 1;
                else if (scheduleSettings.RunDaily)
                    scheduleSettings.RunFrequency = 2;
                else if (scheduleSettings.RunHourly)
                    scheduleSettings.RunFrequency = 3;
            }

            // 设置频率单选按钮
            if (yearlyRadioButton != null)
                yearlyRadioButton.Checked = scheduleSettings.RunFrequency == 0;

            if (monthlyRadioButton != null)
                monthlyRadioButton.Checked = scheduleSettings.RunFrequency == 1;

            if (dailyRadioButton != null)
                dailyRadioButton.Checked = scheduleSettings.RunFrequency == 2;

            if (hourlyRadioButton != null)
                hourlyRadioButton.Checked = scheduleSettings.RunFrequency == 3;

            // 一次性启动设置
            if (oneTimeDateTimePicker != null)
                oneTimeDateTimePicker.Value = scheduleSettings.OneTimeRunTime;

            // 指定时间启动设置 - 按频率更新相应的日期时间选择器
            if (datePicker != null)
                datePicker.Value = new DateTime(
                    DateTime.Now.Year,
                    scheduleSettings.Month,
                    scheduleSettings.Day);

            if (timePicker != null)
                timePicker.Value = new DateTime(
                    DateTime.Now.Year,
                    DateTime.Now.Month,
                    DateTime.Now.Day,
                    scheduleSettings.Hour,
                    scheduleSettings.Minute,
                    scheduleSettings.Second);

            if (monthDayPicker != null)
                monthDayPicker.Value = new DateTime(
                    DateTime.Now.Year,
                    DateTime.Now.Month,
                    scheduleSettings.Day);

            if (dayTimePicker != null)
                dayTimePicker.Value = new DateTime(
                    DateTime.Now.Year,
                    DateTime.Now.Month,
                    DateTime.Now.Day,
                    scheduleSettings.Hour,
                    scheduleSettings.Minute,
                    scheduleSettings.Second);

            if (hourMinSecPicker != null)
                hourMinSecPicker.Value = new DateTime(
                    DateTime.Now.Year,
                    DateTime.Now.Month,
                    DateTime.Now.Day,
                    scheduleSettings.Hour,
                    scheduleSettings.Minute,
                    scheduleSettings.Second);

            // 定时启动设置
            if (startDateTimePicker != null)
                startDateTimePicker.Value = scheduleSettings.StartTime;

            // 如果IntervalDays是默认值0但用户之前可能有间隔设置，保留为0
            if (daysNumericUpDown != null)
                daysNumericUpDown.Value = scheduleSettings.IntervalDays;

            if (hoursNumericUpDown != null)
                hoursNumericUpDown.Value = scheduleSettings.IntervalHours;

            if (minutesNumericUpDown != null)
                minutesNumericUpDown.Value = scheduleSettings.IntervalMinutes;

            if (secondsNumericUpDown != null)
                secondsNumericUpDown.Value = scheduleSettings.IntervalSeconds;

            // 高级设置 - 临时禁用事件处理，避免在加载时触发互斥逻辑
            _isUpdatingAdvancedControls = true;
            try
            {
                if (noLimitRadioButton != null)
                    noLimitRadioButton.Checked = !scheduleSettings.UseLimitedRuns && !scheduleSettings.UseExpirationTime;

                if (limitRunsRadioButton != null)
                    limitRunsRadioButton.Checked = scheduleSettings.UseLimitedRuns;

                if (expirationRadioButton != null)
                    expirationRadioButton.Checked = scheduleSettings.UseExpirationTime;
            }
            finally
            {
                _isUpdatingAdvancedControls = false;
            }

            // 确保高级设置的互斥逻辑正确应用
            UpdateAdvancedControlsState();

            // 确保MaxRunCount不小于NumericUpDown的最小值
            if (maxRunsNumericUpDown != null)
                maxRunsNumericUpDown.Value = scheduleSettings.MaxRunCount > 0 ? scheduleSettings.MaxRunCount : 1;

            if (expirationDateTimePicker != null)
                expirationDateTimePicker.Value = scheduleSettings.ExpirationTime;

            // 更新面板显示
            UpdateDisplayPanels();
        }

        // 更新控件状态
        private void UpdateControlsState()
        {
            bool enabled = enabledCheckBox?.Checked == true;

            // 更新模式选择按钮状态
            if (oneTimeRadioButton != null)
                oneTimeRadioButton.Enabled = enabled;

            if (specificTimeRadioButton != null)
                specificTimeRadioButton.Enabled = enabled;

            if (intervalRadioButton != null)
                intervalRadioButton.Enabled = enabled;

            // 更新一次性启动面板控件状态
            if (oneTimeDateTimePicker != null)
                oneTimeDateTimePicker.Enabled = enabled && oneTimeRadioButton?.Checked == true;

            // 更新指定时间面板控件状态
            bool specificEnabled = enabled && specificTimeRadioButton?.Checked == true;

            if (yearlyRadioButton != null)
                yearlyRadioButton.Enabled = specificEnabled;

            if (monthlyRadioButton != null)
                monthlyRadioButton.Enabled = specificEnabled;

            if (dailyRadioButton != null)
                dailyRadioButton.Enabled = specificEnabled;

            if (hourlyRadioButton != null)
                hourlyRadioButton.Enabled = specificEnabled;

            // 修复年度面板的控件状态
            if (yearlyPanel != null)
            {
                yearlyPanel.Visible = specificEnabled && yearlyRadioButton?.Checked == true;
                yearlyPanel.Enabled = specificEnabled && yearlyRadioButton?.Checked == true;
            }

            if (datePicker != null)
                datePicker.Enabled = specificEnabled && yearlyRadioButton?.Checked == true;

            if (timePicker != null)
                timePicker.Enabled = specificEnabled && yearlyRadioButton?.Checked == true;

            // 修复月度面板的控件状态
            if (monthlyPanel != null)
            {
                monthlyPanel.Visible = specificEnabled && monthlyRadioButton?.Checked == true;
                monthlyPanel.Enabled = specificEnabled && monthlyRadioButton?.Checked == true;
            }

            if (monthDayPicker != null)
                monthDayPicker.Enabled = specificEnabled && monthlyRadioButton?.Checked == true;

            if (dayTimePicker != null)
                dayTimePicker.Enabled = specificEnabled && monthlyRadioButton?.Checked == true;

            // 修复日度面板的控件状态
            if (dailyPanel != null)
            {
                dailyPanel.Visible = specificEnabled && dailyRadioButton?.Checked == true;
                dailyPanel.Enabled = specificEnabled && dailyRadioButton?.Checked == true;
            }

            if (hourMinSecPicker != null)
                hourMinSecPicker.Enabled = specificEnabled && dailyRadioButton?.Checked == true;

            // 修复小时面板的控件状态
            if (hourlyPanel != null)
            {
                hourlyPanel.Visible = specificEnabled && hourlyRadioButton?.Checked == true;
                hourlyPanel.Enabled = specificEnabled && hourlyRadioButton?.Checked == true;
            }

            // 更新定时启动面板控件状态
            bool intervalEnabled = enabled && intervalRadioButton?.Checked == true;

            if (startDateTimePicker != null)
                startDateTimePicker.Enabled = intervalEnabled;

            if (daysNumericUpDown != null)
                daysNumericUpDown.Enabled = intervalEnabled;

            if (hoursNumericUpDown != null)
                hoursNumericUpDown.Enabled = intervalEnabled;

            if (minutesNumericUpDown != null)
                minutesNumericUpDown.Enabled = intervalEnabled;

            if (secondsNumericUpDown != null)
                secondsNumericUpDown.Enabled = intervalEnabled;

            // 更新高级设置面板控件状态
            if (advancedGroupBox != null)
                advancedGroupBox.Enabled = enabled;

            if (noLimitRadioButton != null)
                noLimitRadioButton.Enabled = enabled;

            if (limitRunsRadioButton != null)
                limitRunsRadioButton.Enabled = enabled;

            if (expirationRadioButton != null)
                expirationRadioButton.Enabled = enabled;

            if (maxRunsNumericUpDown != null)
                maxRunsNumericUpDown.Enabled = enabled && limitRunsRadioButton?.Checked == true;

            if (expirationDateTimePicker != null)
                expirationDateTimePicker.Enabled = enabled && expirationRadioButton?.Checked == true;
        }

        // 确定按钮点击事件
        private void OkButton_Click(object? sender, EventArgs e)
        {
            // 保存基本设置
            scheduleSettings.Enabled = enabledCheckBox?.Checked == true;

            if (oneTimeRadioButton?.Checked == true)
                scheduleSettings.ScheduleMode = 0;
            else if (specificTimeRadioButton?.Checked == true)
                scheduleSettings.ScheduleMode = 1;
            else if (intervalRadioButton?.Checked == true)
                scheduleSettings.ScheduleMode = 2;

            // 保存频率设置
            if (yearlyRadioButton?.Checked == true)
                scheduleSettings.RunFrequency = 0;
            else if (monthlyRadioButton?.Checked == true)
                scheduleSettings.RunFrequency = 1;
            else if (dailyRadioButton?.Checked == true)
                scheduleSettings.RunFrequency = 2;
            else if (hourlyRadioButton?.Checked == true)
                scheduleSettings.RunFrequency = 3;

            // 保存一次性启动设置
            if (oneTimeDateTimePicker != null)
                scheduleSettings.OneTimeRunTime = oneTimeDateTimePicker.Value;

            // 保存指定时间启动设置
            if (yearlyRadioButton?.Checked == true && datePicker != null && timePicker != null)
            {
                scheduleSettings.Month = datePicker.Value.Month;
                scheduleSettings.Day = datePicker.Value.Day;
                scheduleSettings.Hour = timePicker.Value.Hour;
                scheduleSettings.Minute = timePicker.Value.Minute;
                scheduleSettings.Second = timePicker.Value.Second;
            }
            else if (monthlyRadioButton?.Checked == true && monthDayPicker != null && dayTimePicker != null)
            {
                scheduleSettings.Day = monthDayPicker.Value.Day;
                scheduleSettings.Hour = dayTimePicker.Value.Hour;
                scheduleSettings.Minute = dayTimePicker.Value.Minute;
                scheduleSettings.Second = dayTimePicker.Value.Second;
            }
            else if (dailyRadioButton?.Checked == true && hourMinSecPicker != null)
            {
                scheduleSettings.Hour = hourMinSecPicker.Value.Hour;
                scheduleSettings.Minute = hourMinSecPicker.Value.Minute;
                scheduleSettings.Second = hourMinSecPicker.Value.Second;
            }
            else if (hourlyRadioButton?.Checked == true && hourlyPanel != null)
            {
                // 修正小时设置处理方式，查找带有特定Tag的DateTimePicker
                DateTimePicker? minuteSecondPicker = null;

                foreach (Control panelControl in hourlyPanel.Controls)
                {
                    if (panelControl is GroupBox groupBox)
                    {
                        foreach (Control groupControl in groupBox.Controls)
                        {
                            if (groupControl is TableLayoutPanel tablePanel)
                            {
                                foreach (Control c in tablePanel.Controls)
                                {
                                    if (c is DateTimePicker picker && picker.Tag?.ToString() == "hourlyPicker")
                                    {
                                        minuteSecondPicker = picker;
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }

                if (minuteSecondPicker != null)
                {
                    scheduleSettings.Minute = minuteSecondPicker.Value.Minute;
                    scheduleSettings.Second = minuteSecondPicker.Value.Second;
                }
            }

            // 更新旧版本兼容性属性
            scheduleSettings.RunYearly = (scheduleSettings.RunFrequency == 0);
            scheduleSettings.RunMonthly = (scheduleSettings.RunFrequency == 1);
            scheduleSettings.RunDaily = (scheduleSettings.RunFrequency == 2);
            scheduleSettings.RunHourly = (scheduleSettings.RunFrequency == 3);

            // 保存定时启动设置
            if (startDateTimePicker != null)
                scheduleSettings.StartTime = startDateTimePicker.Value;

            if (daysNumericUpDown != null)
                scheduleSettings.IntervalDays = (int)daysNumericUpDown.Value;

            if (hoursNumericUpDown != null)
                scheduleSettings.IntervalHours = (int)hoursNumericUpDown.Value;

            if (minutesNumericUpDown != null)
                scheduleSettings.IntervalMinutes = (int)minutesNumericUpDown.Value;

            if (secondsNumericUpDown != null)
                scheduleSettings.IntervalSeconds = (int)secondsNumericUpDown.Value;

            // 保存高级设置
            scheduleSettings.UseLimitedRuns = limitRunsRadioButton?.Checked == true;
            scheduleSettings.UseExpirationTime = expirationRadioButton?.Checked == true;

            if (maxRunsNumericUpDown != null)
                scheduleSettings.MaxRunCount = (int)maxRunsNumericUpDown.Value;

            if (expirationDateTimePicker != null)
                scheduleSettings.ExpirationTime = expirationDateTimePicker.Value;

            // 验证设置
            if (scheduleSettings.Enabled)
            {
                // 检查一次性启动设置
                if (scheduleSettings.ScheduleMode == 0)
                {
                    if (scheduleSettings.OneTimeRunTime <= DateTime.Now)
                    {
                        MessageBox.Show("一次性启动时间必须在当前时间之后", "设置错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }
                }
                // 检查定时启动设置
                else if (scheduleSettings.ScheduleMode == 2)
                {
                    int totalSeconds = scheduleSettings.IntervalDays * 86400 +
                                       scheduleSettings.IntervalHours * 3600 +
                                       scheduleSettings.IntervalMinutes * 60 +
                                       scheduleSettings.IntervalSeconds;

                    if (totalSeconds <= 0)
                    {
                        MessageBox.Show("间隔时间必须大于0秒", "设置错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }

                    // 优化：添加起始时间验证，确保起始时间在当前时间之后
                    if (scheduleSettings.StartTime < DateTime.Now)
                    {
                        DialogResult result = MessageBox.Show(
                            "起始时间已过，任务将立即开始执行。是否继续？",
                            "时间确认",
                            MessageBoxButtons.YesNo,
                            MessageBoxIcon.Question);

                        if (result == DialogResult.No)
                        {
                            return;
                        }
                    }
                }

                // 检查高级设置
                if (scheduleSettings.UseLimitedRuns && scheduleSettings.MaxRunCount <= 0)
                {
                    MessageBox.Show("运行次数限制必须大于0", "设置错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                if (scheduleSettings.UseExpirationTime && scheduleSettings.ExpirationTime <= DateTime.Now)
                {
                    MessageBox.Show("过期时间必须在当前时间之后", "设置错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
            }

            DialogResult = DialogResult.OK;
        }

        // 更新总间隔时间显示 - 保留此方法以避免引用错误
        private void UpdateTotalIntervalTime()
        {
            // 方法已被新的事件处理器替代，保留空方法体以避免错误
        }
    }
}