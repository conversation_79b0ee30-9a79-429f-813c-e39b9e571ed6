using System;
using System.Drawing;
using System.Windows.Forms;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 图片布局设置窗体
    /// </summary>
    public partial class PictureLayoutSettingsForm : Form
    {
        #region 私有字段

        /// <summary>
        /// 图片布局设置
        /// </summary>
        public PictureLayoutSettings Settings { get; private set; } = new PictureLayoutSettings();

        #region 控件字段
        private NumericUpDown? _nudPictureWidth, _nudPictureHeight;
        private ComboBox? _cmbPicturePosition, _cmbTitlePosition, _cmbDescriptionPosition;
        private ComboBox? _cmbArrangementType, _cmbPictureCount;
        private NumericUpDown? _nudPictureSpacing, _nudRowSpacing, _nudColumnSpacing;
        private CheckBox? _chkShowTitle, _chkShowDescription, _chkKeepAspectRatio, _chkAutoResize;
        private CheckBox? _chkShowBorder, _chkShowShadow, _chkRoundedCorners;
        private Button? _btnBorderColor, _btnShadowColor, _btnBackgroundColor;
        private ComboBox? _cmbBorderStyle;
        private NumericUpDown? _nudBorderWidth, _nudCornerRadius, _nudShadowOffset;
        private Button? _btnOK, _btnCancel, _btnReset, _btnPreview;

        // 颜色存储
        private Color _borderColor = Color.Gray;
        private Color _shadowColor = Color.LightGray;
        private Color _backgroundColor = Color.White;
        #endregion

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public PictureLayoutSettingsForm()
        {
            InitializeComponent();
            InitializeControls();
            LoadDefaultValues();
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(PictureLayoutSettingsForm));
            SuspendLayout();
            // 
            // PictureLayoutSettingsForm
            // 
            ClientSize = new Size(634, 561);
            Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            Icon = (Icon)resources.GetObject("$this.Icon");
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "PictureLayoutSettingsForm";
            StartPosition = FormStartPosition.CenterParent;
            Text = "图片布局设置";
            ResumeLayout(false);
        }

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void InitializeControls()
        {
            CreatePictureSizeGroup();
            CreatePositionGroup();
            CreateArrangementGroup();
            CreateTitleDescriptionGroup();
            CreateBorderEffectGroup();
            CreateActionButtons();
        }

        /// <summary>
        /// 创建图片尺寸组
        /// </summary>
        private void CreatePictureSizeGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "图片尺寸设置",
                Location = new Point(20, 20),
                Size = new Size(600, 80),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            var lblWidth = new Label
            {
                Text = "图片宽度:",
                Location = new Point(20, 25),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _nudPictureWidth = new NumericUpDown
            {
                Location = new Point(100, 23),
                Size = new Size(80, 23),
                Minimum = 50,
                Maximum = 1000,
                Value = 300,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            var lblWidthUnit = new Label { Text = "像素", Location = new Point(190, 25), Size = new Size(30, 20) };

            var lblHeight = new Label
            {
                Text = "图片高度:",
                Location = new Point(240, 25),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _nudPictureHeight = new NumericUpDown
            {
                Location = new Point(320, 23),
                Size = new Size(80, 23),
                Minimum = 50,
                Maximum = 800,
                Value = 200,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            var lblHeightUnit = new Label { Text = "像素", Location = new Point(410, 25), Size = new Size(30, 20) };

            _chkKeepAspectRatio = new CheckBox
            {
                Text = "保持宽高比",
                Location = new Point(460, 25),
                Size = new Size(100, 20),
                Checked = true,
                Font = new Font("Microsoft YaHei UI", 8F)
            };

            _chkAutoResize = new CheckBox
            {
                Text = "自动调整大小",
                Location = new Point(20, 50),
                Size = new Size(120, 20),
                Font = new Font("Microsoft YaHei UI", 8F)
            };

            groupBox.Controls.AddRange(new Control[] {
                lblWidth, _nudPictureWidth, lblWidthUnit, lblHeight, _nudPictureHeight, lblHeightUnit,
                _chkKeepAspectRatio, _chkAutoResize
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建位置组
        /// </summary>
        private void CreatePositionGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "图片位置",
                Location = new Point(20, 110),
                Size = new Size(600, 80),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            var lblPosition = new Label
            {
                Text = "图片位置:",
                Location = new Point(20, 25),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbPicturePosition = new ComboBox
            {
                Location = new Point(100, 23),
                Size = new Size(120, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            var lblCount = new Label
            {
                Text = "图片数量:",
                Location = new Point(240, 25),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbPictureCount = new ComboBox
            {
                Location = new Point(320, 23),
                Size = new Size(100, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            groupBox.Controls.AddRange(new Control[] {
                lblPosition, _cmbPicturePosition, lblCount, _cmbPictureCount
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建排列组
        /// </summary>
        private void CreateArrangementGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "排列方式",
                Location = new Point(20, 200),
                Size = new Size(600, 100),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            var lblArrangement = new Label
            {
                Text = "排列类型:",
                Location = new Point(20, 25),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbArrangementType = new ComboBox
            {
                Location = new Point(100, 23),
                Size = new Size(120, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            var lblSpacing = new Label
            {
                Text = "图片间距:",
                Location = new Point(240, 25),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _nudPictureSpacing = new NumericUpDown
            {
                Location = new Point(320, 23),
                Size = new Size(60, 23),
                Minimum = 0,
                Maximum = 100,
                Value = 10,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            var lblSpacingUnit = new Label { Text = "像素", Location = new Point(390, 25), Size = new Size(30, 20) };

            // 行列间距
            var lblRowSpacing = new Label
            {
                Text = "行间距:",
                Location = new Point(20, 55),
                Size = new Size(60, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _nudRowSpacing = new NumericUpDown
            {
                Location = new Point(90, 53),
                Size = new Size(60, 23),
                Minimum = 0,
                Maximum = 100,
                Value = 15
            };

            var lblColumnSpacing = new Label
            {
                Text = "列间距:",
                Location = new Point(170, 55),
                Size = new Size(60, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _nudColumnSpacing = new NumericUpDown
            {
                Location = new Point(240, 53),
                Size = new Size(60, 23),
                Minimum = 0,
                Maximum = 100,
                Value = 15
            };

            groupBox.Controls.AddRange(new Control[] {
                lblArrangement, _cmbArrangementType, lblSpacing, _nudPictureSpacing, lblSpacingUnit,
                lblRowSpacing, _nudRowSpacing, lblColumnSpacing, _nudColumnSpacing
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建标题说明组
        /// </summary>
        private void CreateTitleDescriptionGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "标题和说明",
                Location = new Point(20, 310),
                Size = new Size(600, 100),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            _chkShowTitle = new CheckBox
            {
                Text = "显示标题",
                Location = new Point(20, 25),
                Size = new Size(80, 20),
                Font = new Font("Microsoft YaHei UI", 8F)
            };

            var lblTitlePos = new Label
            {
                Text = "标题位置:",
                Location = new Point(120, 25),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbTitlePosition = new ComboBox
            {
                Location = new Point(200, 23),
                Size = new Size(100, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            _chkShowDescription = new CheckBox
            {
                Text = "显示说明",
                Location = new Point(320, 25),
                Size = new Size(80, 20),
                Font = new Font("Microsoft YaHei UI", 8F)
            };

            var lblDescPos = new Label
            {
                Text = "说明位置:",
                Location = new Point(420, 25),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbDescriptionPosition = new ComboBox
            {
                Location = new Point(500, 23),
                Size = new Size(100, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            groupBox.Controls.AddRange(new Control[] {
                _chkShowTitle, lblTitlePos, _cmbTitlePosition, _chkShowDescription, lblDescPos, _cmbDescriptionPosition
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建边框效果组
        /// </summary>
        private void CreateBorderEffectGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "边框和效果",
                Location = new Point(20, 420),
                Size = new Size(600, 120),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 第一行
            _chkShowBorder = new CheckBox
            {
                Text = "显示边框",
                Location = new Point(20, 25),
                Size = new Size(80, 20),
                Font = new Font("Microsoft YaHei UI", 8F)
            };

            var lblBorderStyle = new Label { Text = "样式:", Location = new Point(120, 25), Size = new Size(40, 20) };
            _cmbBorderStyle = new ComboBox
            {
                Location = new Point(170, 23),
                Size = new Size(80, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 8F)
            };

            var lblBorderWidth = new Label { Text = "宽度:", Location = new Point(270, 25), Size = new Size(40, 20) };
            _nudBorderWidth = new NumericUpDown
            {
                Location = new Point(320, 23),
                Size = new Size(50, 23),
                Minimum = 1,
                Maximum = 20,
                Value = 2
            };

            var lblBorderColor = new Label { Text = "颜色:", Location = new Point(390, 25), Size = new Size(40, 20) };
            _btnBorderColor = new Button
            {
                Location = new Point(440, 23),
                Size = new Size(30, 23),
                BackColor = _borderColor,
                FlatStyle = FlatStyle.Flat
            };
            _btnBorderColor.Click += (s, e) => SelectColor(ref _borderColor, _btnBorderColor);

            // 第二行
            _chkShowShadow = new CheckBox
            {
                Text = "显示阴影",
                Location = new Point(20, 55),
                Size = new Size(80, 20),
                Font = new Font("Microsoft YaHei UI", 8F)
            };

            var lblShadowOffset = new Label { Text = "偏移:", Location = new Point(120, 55), Size = new Size(40, 20) };
            _nudShadowOffset = new NumericUpDown
            {
                Location = new Point(170, 53),
                Size = new Size(50, 23),
                Minimum = 1,
                Maximum = 20,
                Value = 5
            };

            var lblShadowColor = new Label { Text = "颜色:", Location = new Point(240, 55), Size = new Size(40, 20) };
            _btnShadowColor = new Button
            {
                Location = new Point(290, 53),
                Size = new Size(30, 23),
                BackColor = _shadowColor,
                FlatStyle = FlatStyle.Flat
            };
            _btnShadowColor.Click += (s, e) => SelectColor(ref _shadowColor, _btnShadowColor);

            _chkRoundedCorners = new CheckBox
            {
                Text = "圆角",
                Location = new Point(340, 55),
                Size = new Size(60, 20),
                Font = new Font("Microsoft YaHei UI", 8F)
            };

            var lblCornerRadius = new Label { Text = "半径:", Location = new Point(420, 55), Size = new Size(40, 20) };
            _nudCornerRadius = new NumericUpDown
            {
                Location = new Point(470, 53),
                Size = new Size(50, 23),
                Minimum = 1,
                Maximum = 50,
                Value = 10
            };

            // 第三行
            var lblBackground = new Label { Text = "背景颜色:", Location = new Point(20, 85), Size = new Size(70, 20) };
            _btnBackgroundColor = new Button
            {
                Location = new Point(100, 83),
                Size = new Size(50, 25),
                BackColor = _backgroundColor,
                FlatStyle = FlatStyle.Flat
            };
            _btnBackgroundColor.Click += (s, e) => SelectColor(ref _backgroundColor, _btnBackgroundColor);

            groupBox.Controls.AddRange(new Control[] {
                _chkShowBorder, lblBorderStyle, _cmbBorderStyle, lblBorderWidth, _nudBorderWidth, lblBorderColor, _btnBorderColor,
                _chkShowShadow, lblShadowOffset, _nudShadowOffset, lblShadowColor, _btnShadowColor,
                _chkRoundedCorners, lblCornerRadius, _nudCornerRadius,
                lblBackground, _btnBackgroundColor
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建操作按钮
        /// </summary>
        private void CreateActionButtons()
        {
            _btnPreview = new Button
            {
                Text = "预览",
                Location = new Point(380, 560),
                Size = new Size(60, 30),
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            _btnPreview.Click += BtnPreview_Click;

            _btnOK = new Button
            {
                Text = "确定",
                Location = new Point(450, 560),
                Size = new Size(60, 30),
                DialogResult = DialogResult.OK,
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            _btnOK.Click += BtnOK_Click;

            _btnCancel = new Button
            {
                Text = "取消",
                Location = new Point(520, 560),
                Size = new Size(60, 30),
                DialogResult = DialogResult.Cancel,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            _btnReset = new Button
            {
                Text = "重置",
                Location = new Point(590, 560),
                Size = new Size(50, 30),
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            _btnReset.Click += BtnReset_Click;

            this.Controls.AddRange(new Control[] { _btnPreview, _btnOK, _btnCancel, _btnReset });
        }

        /// <summary>
        /// 加载默认值
        /// </summary>
        private void LoadDefaultValues()
        {
            // 图片位置选项
            var positions = new string[] { "居中", "左上", "右上", "左下", "右下", "左侧", "右侧", "顶部", "底部" };
            _cmbPicturePosition?.Items.AddRange(positions);
            if (_cmbPicturePosition != null) _cmbPicturePosition.SelectedItem = "居中";

            // 图片数量选项
            var counts = new string[] { "单张", "2张", "3张", "4张", "6张", "9张", "自定义" };
            _cmbPictureCount?.Items.AddRange(counts);
            if (_cmbPictureCount != null) _cmbPictureCount.SelectedItem = "单张";

            // 排列类型
            var arrangements = new string[] { "网格排列", "水平排列", "垂直排列", "自由排列", "瀑布流" };
            _cmbArrangementType?.Items.AddRange(arrangements);
            if (_cmbArrangementType != null) _cmbArrangementType.SelectedItem = "网格排列";

            // 标题位置
            var titlePositions = new string[] { "图片上方", "图片下方", "图片左侧", "图片右侧", "图片内部" };
            _cmbTitlePosition?.Items.AddRange(titlePositions);
            if (_cmbTitlePosition != null) _cmbTitlePosition.SelectedItem = "图片下方";

            // 说明位置
            var descPositions = new string[] { "标题下方", "图片下方", "图片右侧", "图片内部", "不显示" };
            _cmbDescriptionPosition?.Items.AddRange(descPositions);
            if (_cmbDescriptionPosition != null) _cmbDescriptionPosition.SelectedItem = "标题下方";

            // 边框样式
            var borderStyles = new string[] { "实线", "虚线", "点线", "双线", "无边框" };
            _cmbBorderStyle?.Items.AddRange(borderStyles);
            if (_cmbBorderStyle != null) _cmbBorderStyle.SelectedItem = "实线";

            // 设置控件文字对齐
            SetControlsTextAlignment();
        }

        /// <summary>
        /// 设置控件文字对齐
        /// </summary>
        private void SetControlsTextAlignment()
        {
            // 设置数值输入框文字居中
            SetNumericUpDownTextAlign(_nudPictureWidth);
            SetNumericUpDownTextAlign(_nudPictureHeight);
            SetNumericUpDownTextAlign(_nudPictureSpacing);
            SetNumericUpDownTextAlign(_nudRowSpacing);
            SetNumericUpDownTextAlign(_nudColumnSpacing);
            SetNumericUpDownTextAlign(_nudBorderWidth);
            SetNumericUpDownTextAlign(_nudShadowOffset);
            SetNumericUpDownTextAlign(_nudCornerRadius);

            // 设置下拉框文字居中
            SetComboBoxTextAlign(_cmbPicturePosition);
            SetComboBoxTextAlign(_cmbPictureCount);
            SetComboBoxTextAlign(_cmbArrangementType);
            SetComboBoxTextAlign(_cmbTitlePosition);
            SetComboBoxTextAlign(_cmbDescriptionPosition);
            SetComboBoxTextAlign(_cmbBorderStyle);
        }

        /// <summary>
        /// 设置NumericUpDown文字居中显示
        /// </summary>
        private static void SetNumericUpDownTextAlign(NumericUpDown? numericUpDown)
        {
            if (numericUpDown != null)
                numericUpDown.TextAlign = HorizontalAlignment.Center;
        }

        /// <summary>
        /// 设置ComboBox文字居中显示
        /// </summary>
        private static void SetComboBoxTextAlign(ComboBox? comboBox)
        {
            if (comboBox == null) return;

            comboBox.DrawMode = DrawMode.OwnerDrawFixed;
            comboBox.DrawItem += (sender, e) =>
            {
                if (e.Index < 0) return;

                e.DrawBackground();

                var text = comboBox.Items[e.Index].ToString();
                if (!string.IsNullOrEmpty(text))
                {
                    var textBounds = e.Bounds;
                    var textFlags = TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter;
                    // 修复编译警告：确保字体不为null
                    var font = e.Font ?? comboBox.Font ?? SystemFonts.DefaultFont;
                    TextRenderer.DrawText(e.Graphics, text, font, textBounds, e.ForeColor, textFlags);
                }

                e.DrawFocusRectangle();
            };
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 选择颜色
        /// </summary>
        /// <param name="colorField">颜色字段</param>
        /// <param name="button">颜色按钮</param>
        private void SelectColor(ref Color colorField, Button? button)
        {
            if (button == null) return;

            using var colorDialog = new ColorDialog
            {
                Color = colorField,
                FullOpen = true
            };

            if (colorDialog.ShowDialog(this) == DialogResult.OK)
            {
                colorField = colorDialog.Color;
                button.BackColor = colorField;
            }
        }

        #endregion

        #region 事件处理方法

        /// <summary>
        /// 预览按钮点击事件
        /// </summary>
        private void BtnPreview_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("预览功能显示当前图片布局的效果。\n\n" +
                           "在实际应用中，这里会显示布局预览图。", "布局预览",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void BtnOK_Click(object? sender, EventArgs e)
        {
            try
            {
                // 收集设置
                Settings = new PictureLayoutSettings
                {
                    PictureWidth = (int)(_nudPictureWidth?.Value ?? 300),
                    PictureHeight = (int)(_nudPictureHeight?.Value ?? 200),
                    PicturePosition = _cmbPicturePosition?.SelectedItem?.ToString() ?? "居中",
                    PictureCount = _cmbPictureCount?.SelectedItem?.ToString() ?? "单张",
                    ArrangementType = _cmbArrangementType?.SelectedItem?.ToString() ?? "网格排列",
                    PictureSpacing = (int)(_nudPictureSpacing?.Value ?? 10),
                    RowSpacing = (int)(_nudRowSpacing?.Value ?? 15),
                    ColumnSpacing = (int)(_nudColumnSpacing?.Value ?? 15),
                    ShowTitle = _chkShowTitle?.Checked ?? false,
                    TitlePosition = _cmbTitlePosition?.SelectedItem?.ToString() ?? "图片下方",
                    ShowDescription = _chkShowDescription?.Checked ?? false,
                    DescriptionPosition = _cmbDescriptionPosition?.SelectedItem?.ToString() ?? "标题下方",
                    KeepAspectRatio = _chkKeepAspectRatio?.Checked ?? true,
                    AutoResize = _chkAutoResize?.Checked ?? false,
                    ShowBorder = _chkShowBorder?.Checked ?? false,
                    BorderStyle = _cmbBorderStyle?.SelectedItem?.ToString() ?? "实线",
                    BorderWidth = (int)(_nudBorderWidth?.Value ?? 2),
                    BorderColor = _borderColor,
                    ShowShadow = _chkShowShadow?.Checked ?? false,
                    ShadowOffset = (int)(_nudShadowOffset?.Value ?? 5),
                    ShadowColor = _shadowColor,
                    RoundedCorners = _chkRoundedCorners?.Checked ?? false,
                    CornerRadius = (int)(_nudCornerRadius?.Value ?? 10),
                    BackgroundColor = _backgroundColor
                };

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存设置时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 重置按钮点击事件
        /// </summary>
        private void BtnReset_Click(object? sender, EventArgs e)
        {
            LoadDefaultValues();

            // 重置数值控件
            if (_nudPictureWidth != null) _nudPictureWidth.Value = 300;
            if (_nudPictureHeight != null) _nudPictureHeight.Value = 200;
            if (_nudPictureSpacing != null) _nudPictureSpacing.Value = 10;
            if (_nudRowSpacing != null) _nudRowSpacing.Value = 15;
            if (_nudColumnSpacing != null) _nudColumnSpacing.Value = 15;
            if (_nudBorderWidth != null) _nudBorderWidth.Value = 2;
            if (_nudShadowOffset != null) _nudShadowOffset.Value = 5;
            if (_nudCornerRadius != null) _nudCornerRadius.Value = 10;

            // 重置复选框
            if (_chkKeepAspectRatio != null) _chkKeepAspectRatio.Checked = true;
            if (_chkAutoResize != null) _chkAutoResize.Checked = false;
            if (_chkShowTitle != null) _chkShowTitle.Checked = false;
            if (_chkShowDescription != null) _chkShowDescription.Checked = false;
            if (_chkShowBorder != null) _chkShowBorder.Checked = false;
            if (_chkShowShadow != null) _chkShowShadow.Checked = false;
            if (_chkRoundedCorners != null) _chkRoundedCorners.Checked = false;

            // 重置颜色
            _borderColor = Color.Gray;
            _shadowColor = Color.LightGray;
            _backgroundColor = Color.White;
            if (_btnBorderColor != null) _btnBorderColor.BackColor = _borderColor;
            if (_btnShadowColor != null) _btnShadowColor.BackColor = _shadowColor;
            if (_btnBackgroundColor != null) _btnBackgroundColor.BackColor = _backgroundColor;
        }

        #endregion
    }

    /// <summary>
    /// 图片布局设置类
    /// </summary>
    public class PictureLayoutSettings
    {
        public int PictureWidth { get; set; } = 300;
        public int PictureHeight { get; set; } = 200;
        public string PicturePosition { get; set; } = "居中";
        public string PictureCount { get; set; } = "单张";
        public string ArrangementType { get; set; } = "网格排列";
        public int PictureSpacing { get; set; } = 10;
        public int RowSpacing { get; set; } = 15;
        public int ColumnSpacing { get; set; } = 15;
        public bool ShowTitle { get; set; } = false;
        public string TitlePosition { get; set; } = "图片下方";
        public bool ShowDescription { get; set; } = false;
        public string DescriptionPosition { get; set; } = "标题下方";
        public bool KeepAspectRatio { get; set; } = true;
        public bool AutoResize { get; set; } = false;
        public bool ShowBorder { get; set; } = false;
        public string BorderStyle { get; set; } = "实线";
        public int BorderWidth { get; set; } = 2;
        public Color BorderColor { get; set; } = Color.Gray;
        public bool ShowShadow { get; set; } = false;
        public int ShadowOffset { get; set; } = 5;
        public Color ShadowColor { get; set; } = Color.LightGray;
        public bool RoundedCorners { get; set; } = false;
        public int CornerRadius { get; set; } = 10;
        public Color BackgroundColor { get; set; } = Color.White;
    }
}
