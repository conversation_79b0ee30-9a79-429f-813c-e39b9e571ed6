using System;
using System.Drawing;
using System.Windows.Forms;
using System.Collections.Generic;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 自定义布局设置窗体
    /// </summary>
    public partial class CustomLayoutSettingsForm : Form
    {
        #region 私有字段

        /// <summary>
        /// 自定义布局设置
        /// </summary>
        public CustomLayoutSettings Settings { get; private set; } = new CustomLayoutSettings();

        #region 控件字段
        private TextBox? _txtLayoutName;
        private ComboBox? _cmbLayoutTemplate;
        private ListBox? _listPlaceholders;
        private Button? _btnAddPlaceholder, _btnEditPlaceholder, _btnDeletePlaceholder;
        private NumericUpDown? _nudPlaceholderX, _nudPlaceholderY, _nudPlaceholderWidth, _nudPlaceholderHeight;
        private ComboBox? _cmbPlaceholderType;
        private TextBox? _txtPlaceholderName;
        private CheckBox? _chkLockAspectRatio, _chkAutoResize, _chkShowGrid, _chkSnapToGrid;
        private NumericUpDown? _nudGridSize;
        private Button? _btnSaveLayout, _btnLoadLayout, _btnExportLayout, _btnImportLayout;
        private Panel? _pnlPreview;
        private Button? _btnOK, _btnCancel, _btnReset;

        // 占位符列表
        private List<PlaceholderInfo> _placeholders = new List<PlaceholderInfo>();
        #endregion

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public CustomLayoutSettingsForm()
        {
            InitializeComponent();
            InitializeControls();
            LoadDefaultValues();
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(CustomLayoutSettingsForm));
            SuspendLayout();
            // 
            // CustomLayoutSettingsForm
            // 
            ClientSize = new Size(784, 611);
            Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            Icon = (Icon)resources.GetObject("$this.Icon");
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "CustomLayoutSettingsForm";
            StartPosition = FormStartPosition.CenterParent;
            Text = "自定义布局设置";
            ResumeLayout(false);
        }

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void InitializeControls()
        {
            CreateLayoutInfoGroup();
            CreatePlaceholderListGroup();
            CreatePlaceholderEditGroup();
            CreatePreviewGroup();
            CreateOptionsGroup();
            CreateActionButtons();
        }

        /// <summary>
        /// 创建布局信息组
        /// </summary>
        private void CreateLayoutInfoGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "布局信息",
                Location = new Point(20, 20),
                Size = new Size(750, 80),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            var lblName = new Label
            {
                Text = "布局名称:",
                Location = new Point(20, 25),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _txtLayoutName = new TextBox
            {
                Location = new Point(100, 23),
                Size = new Size(150, 23),
                Text = "自定义布局",
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            var lblTemplate = new Label
            {
                Text = "基础模板:",
                Location = new Point(270, 25),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbLayoutTemplate = new ComboBox
            {
                Location = new Point(350, 23),
                Size = new Size(150, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            _btnSaveLayout = new Button
            {
                Text = "保存布局",
                Location = new Point(520, 22),
                Size = new Size(80, 25),
                Font = new Font("Microsoft YaHei UI", 8F)
            };
            _btnSaveLayout.Click += BtnSaveLayout_Click;

            _btnLoadLayout = new Button
            {
                Text = "加载布局",
                Location = new Point(610, 22),
                Size = new Size(80, 25),
                Font = new Font("Microsoft YaHei UI", 8F)
            };
            _btnLoadLayout.Click += BtnLoadLayout_Click;

            groupBox.Controls.AddRange(new Control[] {
                lblName, _txtLayoutName, lblTemplate, _cmbLayoutTemplate, _btnSaveLayout, _btnLoadLayout
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建占位符列表组
        /// </summary>
        private void CreatePlaceholderListGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "占位符列表",
                Location = new Point(20, 110),
                Size = new Size(350, 200),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            _listPlaceholders = new ListBox
            {
                Location = new Point(20, 25),
                Size = new Size(200, 140),
                Font = new Font("Microsoft YaHei UI", 8F)
            };
            _listPlaceholders.SelectedIndexChanged += ListPlaceholders_SelectedIndexChanged;

            _btnAddPlaceholder = new Button
            {
                Text = "添加",
                Location = new Point(240, 25),
                Size = new Size(80, 30),
                Font = new Font("Microsoft YaHei UI", 8F)
            };
            _btnAddPlaceholder.Click += BtnAddPlaceholder_Click;

            _btnEditPlaceholder = new Button
            {
                Text = "编辑",
                Location = new Point(240, 65),
                Size = new Size(80, 30),
                Font = new Font("Microsoft YaHei UI", 8F)
            };
            _btnEditPlaceholder.Click += BtnEditPlaceholder_Click;

            _btnDeletePlaceholder = new Button
            {
                Text = "删除",
                Location = new Point(240, 105),
                Size = new Size(80, 30),
                Font = new Font("Microsoft YaHei UI", 8F)
            };
            _btnDeletePlaceholder.Click += BtnDeletePlaceholder_Click;

            groupBox.Controls.AddRange(new Control[] {
                _listPlaceholders, _btnAddPlaceholder, _btnEditPlaceholder, _btnDeletePlaceholder
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建占位符编辑组
        /// </summary>
        private void CreatePlaceholderEditGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "占位符属性",
                Location = new Point(390, 110),
                Size = new Size(380, 200),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 第一行
            var lblName = new Label { Text = "名称:", Location = new Point(20, 25), Size = new Size(40, 20) };
            _txtPlaceholderName = new TextBox
            {
                Location = new Point(70, 23),
                Size = new Size(100, 23),
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            var lblType = new Label { Text = "类型:", Location = new Point(190, 25), Size = new Size(40, 20) };
            _cmbPlaceholderType = new ComboBox
            {
                Location = new Point(240, 23),
                Size = new Size(120, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            // 第二行 - 位置
            var lblPosition = new Label { Text = "位置:", Location = new Point(20, 55), Size = new Size(40, 20) };
            var lblX = new Label { Text = "X:", Location = new Point(70, 55), Size = new Size(15, 20) };
            _nudPlaceholderX = new NumericUpDown
            {
                Location = new Point(90, 53),
                Size = new Size(60, 23),
                Minimum = 0,
                Maximum = 1000,
                Value = 50
            };

            var lblY = new Label { Text = "Y:", Location = new Point(160, 55), Size = new Size(15, 20) };
            _nudPlaceholderY = new NumericUpDown
            {
                Location = new Point(180, 53),
                Size = new Size(60, 23),
                Minimum = 0,
                Maximum = 800,
                Value = 50
            };

            // 第三行 - 尺寸
            var lblSize = new Label { Text = "尺寸:", Location = new Point(20, 85), Size = new Size(40, 20) };
            var lblWidth = new Label { Text = "宽:", Location = new Point(70, 85), Size = new Size(20, 20) };
            _nudPlaceholderWidth = new NumericUpDown
            {
                Location = new Point(95, 83),
                Size = new Size(60, 23),
                Minimum = 10,
                Maximum = 1000,
                Value = 200
            };

            var lblHeight = new Label { Text = "高:", Location = new Point(165, 85), Size = new Size(20, 20) };
            _nudPlaceholderHeight = new NumericUpDown
            {
                Location = new Point(190, 83),
                Size = new Size(60, 23),
                Minimum = 10,
                Maximum = 800,
                Value = 100
            };

            // 选项
            _chkLockAspectRatio = new CheckBox
            {
                Text = "锁定宽高比",
                Location = new Point(20, 115),
                Size = new Size(100, 20),
                Font = new Font("Microsoft YaHei UI", 8F)
            };

            _chkAutoResize = new CheckBox
            {
                Text = "自动调整大小",
                Location = new Point(140, 115),
                Size = new Size(120, 20),
                Font = new Font("Microsoft YaHei UI", 8F)
            };

            groupBox.Controls.AddRange(new Control[] {
                lblName, _txtPlaceholderName, lblType, _cmbPlaceholderType,
                lblPosition, lblX, _nudPlaceholderX, lblY, _nudPlaceholderY,
                lblSize, lblWidth, _nudPlaceholderWidth, lblHeight, _nudPlaceholderHeight,
                _chkLockAspectRatio, _chkAutoResize
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建预览组
        /// </summary>
        private void CreatePreviewGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "布局预览",
                Location = new Point(20, 320),
                Size = new Size(550, 200),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            _pnlPreview = new Panel
            {
                Location = new Point(20, 25),
                Size = new Size(500, 150),
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.White
            };
            _pnlPreview.Paint += PnlPreview_Paint;

            groupBox.Controls.Add(_pnlPreview);
            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建选项组
        /// </summary>
        private void CreateOptionsGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "编辑选项",
                Location = new Point(590, 320),
                Size = new Size(180, 200),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            _chkShowGrid = new CheckBox
            {
                Text = "显示网格",
                Location = new Point(20, 25),
                Size = new Size(80, 20),
                Checked = true,
                Font = new Font("Microsoft YaHei UI", 8F)
            };

            _chkSnapToGrid = new CheckBox
            {
                Text = "对齐网格",
                Location = new Point(20, 55),
                Size = new Size(80, 20),
                Checked = true,
                Font = new Font("Microsoft YaHei UI", 8F)
            };

            var lblGridSize = new Label
            {
                Text = "网格大小:",
                Location = new Point(20, 85),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _nudGridSize = new NumericUpDown
            {
                Location = new Point(100, 83),
                Size = new Size(60, 23),
                Minimum = 5,
                Maximum = 50,
                Value = 20
            };

            _btnExportLayout = new Button
            {
                Text = "导出布局",
                Location = new Point(20, 120),
                Size = new Size(80, 25),
                Font = new Font("Microsoft YaHei UI", 8F)
            };
            _btnExportLayout.Click += BtnExportLayout_Click;

            _btnImportLayout = new Button
            {
                Text = "导入布局",
                Location = new Point(20, 155),
                Size = new Size(80, 25),
                Font = new Font("Microsoft YaHei UI", 8F)
            };
            _btnImportLayout.Click += BtnImportLayout_Click;

            groupBox.Controls.AddRange(new Control[] {
                _chkShowGrid, _chkSnapToGrid, lblGridSize, _nudGridSize, _btnExportLayout, _btnImportLayout
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建操作按钮
        /// </summary>
        private void CreateActionButtons()
        {
            _btnOK = new Button
            {
                Text = "确定",
                Location = new Point(580, 540),
                Size = new Size(60, 30),
                DialogResult = DialogResult.OK,
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            _btnOK.Click += BtnOK_Click;

            _btnCancel = new Button
            {
                Text = "取消",
                Location = new Point(650, 540),
                Size = new Size(60, 30),
                DialogResult = DialogResult.Cancel,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            _btnReset = new Button
            {
                Text = "重置",
                Location = new Point(720, 540),
                Size = new Size(50, 30),
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            _btnReset.Click += BtnReset_Click;

            this.Controls.AddRange(new Control[] { _btnOK, _btnCancel, _btnReset });
        }

        /// <summary>
        /// 加载默认值
        /// </summary>
        private void LoadDefaultValues()
        {
            // 布局模板
            var templates = new string[] { "空白布局", "标题+内容", "两栏布局", "图片布局", "表格布局" };
            _cmbLayoutTemplate?.Items.AddRange(templates);
            if (_cmbLayoutTemplate != null) _cmbLayoutTemplate.SelectedItem = "空白布局";

            // 占位符类型
            var placeholderTypes = new string[] { "文本", "标题", "图片", "表格", "图表", "形状", "视频", "音频" };
            _cmbPlaceholderType?.Items.AddRange(placeholderTypes);
            if (_cmbPlaceholderType != null) _cmbPlaceholderType.SelectedItem = "文本";

            // 添加默认占位符
            _placeholders.Add(new PlaceholderInfo
            {
                Name = "标题",
                Type = "标题",
                X = 50,
                Y = 50,
                Width = 400,
                Height = 60
            });

            _placeholders.Add(new PlaceholderInfo
            {
                Name = "内容",
                Type = "文本",
                X = 50,
                Y = 130,
                Width = 400,
                Height = 200
            });

            UpdatePlaceholderList();

            // 设置控件文字对齐
            SetControlsTextAlignment();
        }

        /// <summary>
        /// 设置控件文字对齐
        /// </summary>
        private void SetControlsTextAlignment()
        {
            // 设置数值输入框文字居中
            SetNumericUpDownTextAlign(_nudPlaceholderX);
            SetNumericUpDownTextAlign(_nudPlaceholderY);
            SetNumericUpDownTextAlign(_nudPlaceholderWidth);
            SetNumericUpDownTextAlign(_nudPlaceholderHeight);
            SetNumericUpDownTextAlign(_nudGridSize);

            // 设置下拉框文字居中
            SetComboBoxTextAlign(_cmbLayoutTemplate);
            SetComboBoxTextAlign(_cmbPlaceholderType);

            // 设置文本框文字居中
            if (_txtLayoutName != null) _txtLayoutName.TextAlign = HorizontalAlignment.Center;
            if (_txtPlaceholderName != null) _txtPlaceholderName.TextAlign = HorizontalAlignment.Center;
        }

        /// <summary>
        /// 设置NumericUpDown文字居中显示
        /// </summary>
        private static void SetNumericUpDownTextAlign(NumericUpDown? numericUpDown)
        {
            if (numericUpDown != null)
                numericUpDown.TextAlign = HorizontalAlignment.Center;
        }

        /// <summary>
        /// 设置ComboBox文字居中显示
        /// </summary>
        private static void SetComboBoxTextAlign(ComboBox? comboBox)
        {
            if (comboBox == null) return;

            comboBox.DrawMode = DrawMode.OwnerDrawFixed;
            comboBox.DrawItem += (sender, e) =>
            {
                if (e.Index < 0) return;

                e.DrawBackground();

                var text = comboBox.Items[e.Index].ToString();
                if (!string.IsNullOrEmpty(text))
                {
                    var textBounds = e.Bounds;
                    var textFlags = TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter;
                    // 修复编译警告：确保字体不为null
                    var font = e.Font ?? comboBox.Font ?? SystemFonts.DefaultFont;
                    TextRenderer.DrawText(e.Graphics, text, font, textBounds, e.ForeColor, textFlags);
                }

                e.DrawFocusRectangle();
            };
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 更新占位符列表
        /// </summary>
        private void UpdatePlaceholderList()
        {
            _listPlaceholders?.Items.Clear();
            foreach (var placeholder in _placeholders)
            {
                _listPlaceholders?.Items.Add($"{placeholder.Name} ({placeholder.Type})");
            }
            _pnlPreview?.Invalidate();
        }

        #endregion

        #region 事件处理方法

        /// <summary>
        /// 占位符列表选择改变事件
        /// </summary>
        private void ListPlaceholders_SelectedIndexChanged(object? sender, EventArgs e)
        {
            if (_listPlaceholders?.SelectedIndex >= 0 && _listPlaceholders.SelectedIndex < _placeholders.Count)
            {
                var placeholder = _placeholders[_listPlaceholders.SelectedIndex];
                if (_txtPlaceholderName != null) _txtPlaceholderName.Text = placeholder.Name;
                if (_cmbPlaceholderType != null) _cmbPlaceholderType.SelectedItem = placeholder.Type;
                if (_nudPlaceholderX != null) _nudPlaceholderX.Value = placeholder.X;
                if (_nudPlaceholderY != null) _nudPlaceholderY.Value = placeholder.Y;
                if (_nudPlaceholderWidth != null) _nudPlaceholderWidth.Value = placeholder.Width;
                if (_nudPlaceholderHeight != null) _nudPlaceholderHeight.Value = placeholder.Height;
            }
        }

        /// <summary>
        /// 添加占位符按钮点击事件
        /// </summary>
        private void BtnAddPlaceholder_Click(object? sender, EventArgs e)
        {
            var placeholder = new PlaceholderInfo
            {
                Name = _txtPlaceholderName?.Text ?? "新占位符",
                Type = _cmbPlaceholderType?.SelectedItem?.ToString() ?? "文本",
                X = (int)(_nudPlaceholderX?.Value ?? 50),
                Y = (int)(_nudPlaceholderY?.Value ?? 50),
                Width = (int)(_nudPlaceholderWidth?.Value ?? 200),
                Height = (int)(_nudPlaceholderHeight?.Value ?? 100)
            };

            _placeholders.Add(placeholder);
            UpdatePlaceholderList();
        }

        /// <summary>
        /// 编辑占位符按钮点击事件
        /// </summary>
        private void BtnEditPlaceholder_Click(object? sender, EventArgs e)
        {
            if (_listPlaceholders?.SelectedIndex >= 0 && _listPlaceholders.SelectedIndex < _placeholders.Count)
            {
                var placeholder = _placeholders[_listPlaceholders.SelectedIndex];
                placeholder.Name = _txtPlaceholderName?.Text ?? placeholder.Name;
                placeholder.Type = _cmbPlaceholderType?.SelectedItem?.ToString() ?? placeholder.Type;
                placeholder.X = (int)(_nudPlaceholderX?.Value ?? placeholder.X);
                placeholder.Y = (int)(_nudPlaceholderY?.Value ?? placeholder.Y);
                placeholder.Width = (int)(_nudPlaceholderWidth?.Value ?? placeholder.Width);
                placeholder.Height = (int)(_nudPlaceholderHeight?.Value ?? placeholder.Height);

                UpdatePlaceholderList();
            }
        }

        /// <summary>
        /// 删除占位符按钮点击事件
        /// </summary>
        private void BtnDeletePlaceholder_Click(object? sender, EventArgs e)
        {
            if (_listPlaceholders?.SelectedIndex >= 0 && _listPlaceholders.SelectedIndex < _placeholders.Count)
            {
                _placeholders.RemoveAt(_listPlaceholders.SelectedIndex);
                UpdatePlaceholderList();
            }
        }

        /// <summary>
        /// 预览面板绘制事件
        /// </summary>
        private void PnlPreview_Paint(object? sender, PaintEventArgs e)
        {
            if (_pnlPreview == null) return;

            var g = e.Graphics;
            var scale = Math.Min(_pnlPreview.Width / 500.0, _pnlPreview.Height / 375.0);

            // 绘制网格
            if (_chkShowGrid?.Checked == true)
            {
                var gridSize = (int)(_nudGridSize?.Value ?? 20) * scale;
                using var gridPen = new Pen(Color.LightGray, 1);
                for (int x = 0; x < _pnlPreview.Width; x += (int)gridSize)
                {
                    g.DrawLine(gridPen, x, 0, x, _pnlPreview.Height);
                }
                for (int y = 0; y < _pnlPreview.Height; y += (int)gridSize)
                {
                    g.DrawLine(gridPen, 0, y, _pnlPreview.Width, y);
                }
            }

            // 绘制占位符
            foreach (var placeholder in _placeholders)
            {
                var rect = new Rectangle(
                    (int)(placeholder.X * scale),
                    (int)(placeholder.Y * scale),
                    (int)(placeholder.Width * scale),
                    (int)(placeholder.Height * scale)
                );

                using var brush = new SolidBrush(Color.LightBlue);
                using var pen = new Pen(Color.Blue, 2);
                g.FillRectangle(brush, rect);
                g.DrawRectangle(pen, rect);

                using var font = new Font("Microsoft YaHei UI", 8F);
                using var textBrush = new SolidBrush(Color.Black);
                g.DrawString(placeholder.Name, font, textBrush, rect.X + 5, rect.Y + 5);
            }
        }

        /// <summary>
        /// 保存布局按钮点击事件
        /// </summary>
        private void BtnSaveLayout_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("布局保存功能将在后续版本中实现。", "保存布局",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 加载布局按钮点击事件
        /// </summary>
        private void BtnLoadLayout_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("布局加载功能将在后续版本中实现。", "加载布局",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 导出布局按钮点击事件
        /// </summary>
        private void BtnExportLayout_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("布局导出功能将在后续版本中实现。", "导出布局",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 导入布局按钮点击事件
        /// </summary>
        private void BtnImportLayout_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("布局导入功能将在后续版本中实现。", "导入布局",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void BtnOK_Click(object? sender, EventArgs e)
        {
            try
            {
                // 收集设置
                Settings = new CustomLayoutSettings
                {
                    LayoutName = _txtLayoutName?.Text ?? "自定义布局",
                    LayoutTemplate = _cmbLayoutTemplate?.SelectedItem?.ToString() ?? "空白布局",
                    Placeholders = new List<PlaceholderInfo>(_placeholders),
                    ShowGrid = _chkShowGrid?.Checked ?? true,
                    SnapToGrid = _chkSnapToGrid?.Checked ?? true,
                    GridSize = (int)(_nudGridSize?.Value ?? 20)
                };

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存设置时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 重置按钮点击事件
        /// </summary>
        private void BtnReset_Click(object? sender, EventArgs e)
        {
            _placeholders.Clear();
            LoadDefaultValues();

            if (_txtLayoutName != null) _txtLayoutName.Text = "自定义布局";
            if (_chkShowGrid != null) _chkShowGrid.Checked = true;
            if (_chkSnapToGrid != null) _chkSnapToGrid.Checked = true;
            if (_nudGridSize != null) _nudGridSize.Value = 20;
        }

        #endregion
    }

    /// <summary>
    /// 占位符信息类
    /// </summary>
    public class PlaceholderInfo
    {
        public string Name { get; set; } = "";
        public string Type { get; set; } = "";
        public int X { get; set; } = 0;
        public int Y { get; set; } = 0;
        public int Width { get; set; } = 100;
        public int Height { get; set; } = 50;
    }

    /// <summary>
    /// 自定义布局设置类
    /// </summary>
    public class CustomLayoutSettings
    {
        public string LayoutName { get; set; } = "自定义布局";
        public string LayoutTemplate { get; set; } = "空白布局";
        public List<PlaceholderInfo> Placeholders { get; set; } = new List<PlaceholderInfo>();
        public bool ShowGrid { get; set; } = true;
        public bool SnapToGrid { get; set; } = true;
        public int GridSize { get; set; } = 20;
    }
}
