namespace PPTPiliangChuli.Forms
{
    partial class SupportedFormatsForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(SupportedFormatsForm));
            panelFormats = new Panel();
            btnOK = new Button();
            btnCancel = new Button();
            btnSelectAll = new Button();
            btnDeselectAll = new Button();
            lblTitle = new Label();
            lblDescription = new Label();
            SuspendLayout();
            // 
            // panelFormats
            // 
            panelFormats.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            panelFormats.AutoScroll = true;
            panelFormats.BackColor = Color.White;
            panelFormats.BorderStyle = BorderStyle.FixedSingle;
            panelFormats.Location = new Point(12, 85);
            panelFormats.Name = "panelFormats";
            panelFormats.Size = new Size(460, 320);
            panelFormats.TabIndex = 0;
            // 
            // btnOK
            // 
            btnOK.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            btnOK.BackColor = Color.FromArgb(0, 123, 255);
            btnOK.FlatAppearance.BorderSize = 0;
            btnOK.FlatStyle = FlatStyle.Flat;
            btnOK.ForeColor = Color.White;
            btnOK.Location = new Point(315, 420);
            btnOK.Name = "btnOK";
            btnOK.Size = new Size(80, 35);
            btnOK.TabIndex = 1;
            btnOK.Text = "确定";
            btnOK.UseVisualStyleBackColor = false;
            btnOK.Click += BtnOK_Click;
            // 
            // btnCancel
            // 
            btnCancel.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            btnCancel.BackColor = Color.FromArgb(108, 117, 125);
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.FlatStyle = FlatStyle.Flat;
            btnCancel.ForeColor = Color.White;
            btnCancel.Location = new Point(405, 420);
            btnCancel.Name = "btnCancel";
            btnCancel.Size = new Size(80, 35);
            btnCancel.TabIndex = 2;
            btnCancel.Text = "取消";
            btnCancel.UseVisualStyleBackColor = false;
            btnCancel.Click += BtnCancel_Click;
            // 
            // btnSelectAll
            // 
            btnSelectAll.Anchor = AnchorStyles.Bottom | AnchorStyles.Left;
            btnSelectAll.BackColor = Color.FromArgb(40, 167, 69);
            btnSelectAll.FlatAppearance.BorderSize = 0;
            btnSelectAll.FlatStyle = FlatStyle.Flat;
            btnSelectAll.ForeColor = Color.White;
            btnSelectAll.Location = new Point(12, 420);
            btnSelectAll.Name = "btnSelectAll";
            btnSelectAll.Size = new Size(80, 35);
            btnSelectAll.TabIndex = 3;
            btnSelectAll.Text = "全选";
            btnSelectAll.UseVisualStyleBackColor = false;
            btnSelectAll.Click += BtnSelectAll_Click;
            // 
            // btnDeselectAll
            // 
            btnDeselectAll.Anchor = AnchorStyles.Bottom | AnchorStyles.Left;
            btnDeselectAll.BackColor = Color.FromArgb(220, 53, 69);
            btnDeselectAll.FlatAppearance.BorderSize = 0;
            btnDeselectAll.FlatStyle = FlatStyle.Flat;
            btnDeselectAll.ForeColor = Color.White;
            btnDeselectAll.Location = new Point(102, 420);
            btnDeselectAll.Name = "btnDeselectAll";
            btnDeselectAll.Size = new Size(80, 35);
            btnDeselectAll.TabIndex = 4;
            btnDeselectAll.Text = "取消全选";
            btnDeselectAll.UseVisualStyleBackColor = false;
            btnDeselectAll.Click += BtnDeselectAll_Click;
            // 
            // lblTitle
            // 
            lblTitle.AutoSize = true;
            lblTitle.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold, GraphicsUnit.Point);
            lblTitle.ForeColor = Color.FromArgb(33, 37, 41);
            lblTitle.Location = new Point(12, 15);
            lblTitle.Name = "lblTitle";
            lblTitle.Size = new Size(182, 31);
            lblTitle.TabIndex = 5;
            lblTitle.Text = "支持的文件格式";
            // 
            // lblDescription
            // 
            lblDescription.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            lblDescription.ForeColor = Color.FromArgb(108, 117, 125);
            lblDescription.Location = new Point(12, 45);
            lblDescription.Name = "lblDescription";
            lblDescription.Size = new Size(460, 30);
            lblDescription.TabIndex = 6;
            lblDescription.Text = "请选择要处理的PowerPoint文件格式";
            // 
            // SupportedFormatsForm
            // 
            AutoScaleDimensions = new SizeF(12F, 27F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.FromArgb(248, 249, 250);
            ClientSize = new Size(500, 475);
            Controls.Add(lblDescription);
            Controls.Add(lblTitle);
            Controls.Add(btnDeselectAll);
            Controls.Add(btnSelectAll);
            Controls.Add(btnCancel);
            Controls.Add(btnOK);
            Controls.Add(panelFormats);
            Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            Icon = (Icon)resources.GetObject("$this.Icon");
            Name = "SupportedFormatsForm";
            StartPosition = FormStartPosition.CenterParent;
            Text = "支持格式设置";
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private Panel panelFormats;
        private Button btnOK;
        private Button btnCancel;
        private Button btnSelectAll;
        private Button btnDeselectAll;
        private Label lblTitle;
        private Label lblDescription;
    }
}
