/// <summary>
/// 形状样式设置窗体文件
/// 用途：提供PPT文档中形状的样式设置功能，包括填充、边框、阴影、3D效果等
/// 功能：支持纯色填充、渐变填充、图案填充、纹理填充、图片填充等多种填充方式
/// 边框设置：支持各种边框样式、颜色、宽度设置
/// 效果设置：支持阴影、发光、反射、3D等特殊效果
/// 符合Aspose.Slides API规范，使用FillFormat、LineFormat、EffectFormat等接口
/// 作者：PPT批量处理工具
/// 创建时间：2024年
/// </summary>

using System;
using System.Drawing;
using System.Windows.Forms;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 形状样式设置窗体
    /// </summary>
    public partial class ShapeStyleSettingsForm : Form
    {
        #region 私有字段

        /// <summary>
        /// 形状样式设置
        /// </summary>
        public ShapeStyleSettings Settings { get; private set; } = new ShapeStyleSettings();

        #region 控件字段
        private ComboBox? _cmbFillType, _cmbBorderStyle, _cmbShadowType, _cmbEffectType;
        private Button? _btnFillColor, _btnBorderColor, _btnShadowColor, _btnGradientColor1, _btnGradientColor2;
        private NumericUpDown? _nudBorderWidth, _nudShadowOffset, _nudTransparency, _nudGradientAngle;
        private CheckBox? _chkShowBorder, _chkShowShadow, _chkShow3D, _chkGradientFill;
        private ComboBox? _cmbGradientType;
        private Button? _btnOK, _btnCancel, _btnReset, _btnPreview;

        // 颜色存储
        private Color _fillColor = Color.LightBlue;
        private Color _borderColor = Color.Black;
        private Color _shadowColor = Color.Gray;
        private Color _gradientColor1 = Color.White;
        private Color _gradientColor2 = Color.Blue;
        #endregion

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public ShapeStyleSettingsForm()
        {
            InitializeComponent();
            InitializeControls();
            LoadDefaultValues();
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ShapeStyleSettingsForm));
            SuspendLayout();
            // 
            // ShapeStyleSettingsForm
            // 
            ClientSize = new Size(778, 664);
            Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            Icon = (Icon)resources.GetObject("$this.Icon");
            MaximizeBox = false;
            MinimizeBox = false;
            MinimumSize = new Size(800, 720);
            Name = "ShapeStyleSettingsForm";
            StartPosition = FormStartPosition.CenterParent;
            Text = "形状样式设置";
            ResumeLayout(false);
        }

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void InitializeControls()
        {
            CreateFillGroup();
            CreateBorderGroup();
            CreateEffectsGroup();
            CreateActionButtons();
        }

        /// <summary>
        /// 创建填充设置组
        /// </summary>
        private void CreateFillGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "填充设置",
                Location = new Point(30, 30),
                Size = new Size(740, 180),
                Font = new Font("Microsoft YaHei UI", 11F, FontStyle.Bold)
            };

            // 第一行：填充类型、填充颜色、透明度
            var lblFillType = new Label
            {
                Text = "填充类型:",
                Location = new Point(30, 40),
                Size = new Size(90, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };
            _cmbFillType = new ComboBox
            {
                Location = new Point(130, 38),
                Size = new Size(140, 28),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 10F),
                MaxDropDownItems = 8,
                DropDownHeight = 160
            };

            var lblFillColor = new Label
            {
                Text = "填充颜色:",
                Location = new Point(290, 40),
                Size = new Size(90, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };
            _btnFillColor = new Button
            {
                Location = new Point(390, 38),
                Size = new Size(100, 28),
                BackColor = _fillColor,
                Text = "选择颜色",
                Font = new Font("Microsoft YaHei UI", 10F)
            };
            _btnFillColor.Click += BtnFillColor_Click;

            var lblTransparency = new Label
            {
                Text = "透明度(%):",
                Location = new Point(510, 40),
                Size = new Size(90, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };
            _nudTransparency = new NumericUpDown
            {
                Location = new Point(610, 38),
                Size = new Size(80, 28),
                Minimum = 0,
                Maximum = 100,
                Value = 0,
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            // 第二行：渐变填充选项和渐变类型
            _chkGradientFill = new CheckBox
            {
                Text = "渐变填充",
                Location = new Point(30, 80),
                Size = new Size(120, 25),
                Font = new Font("Microsoft YaHei UI", 10F)
            };
            _chkGradientFill.CheckedChanged += ChkGradientFill_CheckedChanged;

            var lblGradientType = new Label
            {
                Text = "渐变类型:",
                Location = new Point(170, 80),
                Size = new Size(90, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };
            _cmbGradientType = new ComboBox
            {
                Location = new Point(270, 78),
                Size = new Size(120, 28),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 10F),
                MaxDropDownItems = 6,
                DropDownHeight = 120
            };

            // 第三行：渐变颜色和角度
            var lblGradientColor1 = new Label
            {
                Text = "起始颜色:",
                Location = new Point(30, 120),
                Size = new Size(90, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };
            _btnGradientColor1 = new Button
            {
                Location = new Point(130, 118),
                Size = new Size(100, 28),
                BackColor = _gradientColor1,
                Text = "起始颜色",
                Font = new Font("Microsoft YaHei UI", 10F)
            };
            _btnGradientColor1.Click += BtnGradientColor1_Click;

            var lblGradientColor2 = new Label
            {
                Text = "结束颜色:",
                Location = new Point(250, 120),
                Size = new Size(90, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };
            _btnGradientColor2 = new Button
            {
                Location = new Point(350, 118),
                Size = new Size(100, 28),
                BackColor = _gradientColor2,
                Text = "结束颜色",
                Font = new Font("Microsoft YaHei UI", 10F)
            };
            _btnGradientColor2.Click += BtnGradientColor2_Click;

            var lblGradientAngle = new Label
            {
                Text = "渐变角度:",
                Location = new Point(470, 120),
                Size = new Size(90, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };
            _nudGradientAngle = new NumericUpDown
            {
                Location = new Point(570, 118),
                Size = new Size(80, 28),
                Minimum = 0,
                Maximum = 360,
                Value = 0,
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            groupBox.Controls.AddRange(new Control[] {
                lblFillType, _cmbFillType, lblFillColor, _btnFillColor, lblTransparency, _nudTransparency,
                _chkGradientFill, lblGradientType, _cmbGradientType,
                lblGradientColor1, _btnGradientColor1, lblGradientColor2, _btnGradientColor2,
                lblGradientAngle, _nudGradientAngle
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建边框设置组
        /// </summary>
        private void CreateBorderGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "边框设置",
                Location = new Point(30, 230),
                Size = new Size(740, 140),
                Font = new Font("Microsoft YaHei UI", 11F, FontStyle.Bold)
            };

            // 第一行：显示边框、边框样式、边框颜色
            _chkShowBorder = new CheckBox
            {
                Text = "显示边框",
                Location = new Point(30, 40),
                Size = new Size(120, 25),
                Font = new Font("Microsoft YaHei UI", 10F)
            };
            _chkShowBorder.CheckedChanged += ChkShowBorder_CheckedChanged;

            var lblBorderStyle = new Label
            {
                Text = "边框样式:",
                Location = new Point(170, 40),
                Size = new Size(90, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };
            _cmbBorderStyle = new ComboBox
            {
                Location = new Point(270, 38),
                Size = new Size(120, 28),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 10F),
                MaxDropDownItems = 8,
                DropDownHeight = 160
            };

            var lblBorderColor = new Label
            {
                Text = "边框颜色:",
                Location = new Point(410, 40),
                Size = new Size(90, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };
            _btnBorderColor = new Button
            {
                Location = new Point(510, 38),
                Size = new Size(100, 28),
                BackColor = _borderColor,
                Text = "选择颜色",
                Font = new Font("Microsoft YaHei UI", 10F)
            };
            _btnBorderColor.Click += BtnBorderColor_Click;

            // 第二行：边框宽度
            var lblBorderWidth = new Label
            {
                Text = "边框宽度:",
                Location = new Point(30, 80),
                Size = new Size(90, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };
            _nudBorderWidth = new NumericUpDown
            {
                Location = new Point(130, 78),
                Size = new Size(80, 28),
                Minimum = 1,
                Maximum = 20,
                Value = 2,
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            groupBox.Controls.AddRange(new Control[] {
                _chkShowBorder, lblBorderStyle, _cmbBorderStyle, lblBorderColor, _btnBorderColor,
                lblBorderWidth, _nudBorderWidth
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建效果设置组
        /// </summary>
        private void CreateEffectsGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "效果设置",
                Location = new Point(30, 390),
                Size = new Size(740, 180),
                Font = new Font("Microsoft YaHei UI", 11F, FontStyle.Bold)
            };

            // 第一行：阴影效果、阴影类型、阴影颜色
            _chkShowShadow = new CheckBox
            {
                Text = "显示阴影",
                Location = new Point(30, 40),
                Size = new Size(120, 25),
                Font = new Font("Microsoft YaHei UI", 10F)
            };
            _chkShowShadow.CheckedChanged += ChkShowShadow_CheckedChanged;

            var lblShadowType = new Label
            {
                Text = "阴影类型:",
                Location = new Point(170, 40),
                Size = new Size(90, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };
            _cmbShadowType = new ComboBox
            {
                Location = new Point(270, 38),
                Size = new Size(120, 28),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 10F),
                MaxDropDownItems = 6,
                DropDownHeight = 120
            };

            var lblShadowColor = new Label
            {
                Text = "阴影颜色:",
                Location = new Point(410, 40),
                Size = new Size(90, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };
            _btnShadowColor = new Button
            {
                Location = new Point(510, 38),
                Size = new Size(100, 28),
                BackColor = _shadowColor,
                Text = "选择颜色",
                Font = new Font("Microsoft YaHei UI", 10F)
            };
            _btnShadowColor.Click += BtnShadowColor_Click;

            // 第二行：阴影偏移、3D效果
            var lblShadowOffset = new Label
            {
                Text = "阴影偏移:",
                Location = new Point(30, 80),
                Size = new Size(90, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };
            _nudShadowOffset = new NumericUpDown
            {
                Location = new Point(130, 78),
                Size = new Size(80, 28),
                Minimum = 0,
                Maximum = 50,
                Value = 5,
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            _chkShow3D = new CheckBox
            {
                Text = "3D效果",
                Location = new Point(240, 80),
                Size = new Size(120, 25),
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            // 第三行：特殊效果类型
            var lblEffectType = new Label
            {
                Text = "特殊效果:",
                Location = new Point(30, 120),
                Size = new Size(90, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };
            _cmbEffectType = new ComboBox
            {
                Location = new Point(130, 118),
                Size = new Size(140, 28),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 10F),
                MaxDropDownItems = 8,
                DropDownHeight = 160
            };

            groupBox.Controls.AddRange(new Control[] {
                _chkShowShadow, lblShadowType, _cmbShadowType, lblShadowColor, _btnShadowColor,
                lblShadowOffset, _nudShadowOffset, _chkShow3D,
                lblEffectType, _cmbEffectType
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建操作按钮
        /// </summary>
        private void CreateActionButtons()
        {
            _btnPreview = new Button
            {
                Text = "预览(&P)",
                Location = new Point(430, 590),
                Size = new Size(90, 35),
                Font = new Font("Microsoft YaHei UI", 10F)
            };
            _btnOK = new Button
            {
                Text = "确定(&O)",
                Location = new Point(535, 590),
                Size = new Size(90, 35),
                Font = new Font("Microsoft YaHei UI", 10F)
            };
            _btnCancel = new Button
            {
                Text = "取消(&C)",
                Location = new Point(640, 590),
                Size = new Size(90, 35),
                Font = new Font("Microsoft YaHei UI", 10F)
            };
            _btnReset = new Button
            {
                Text = "重置(&R)",
                Location = new Point(325, 590),
                Size = new Size(90, 35),
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            _btnOK.Click += BtnOK_Click;
            _btnCancel.Click += BtnCancel_Click;
            _btnReset.Click += BtnReset_Click;
            _btnPreview.Click += BtnPreview_Click;

            this.Controls.AddRange(new Control[] { _btnOK, _btnCancel, _btnReset, _btnPreview });
        }

        /// <summary>
        /// 加载默认值
        /// </summary>
        private void LoadDefaultValues()
        {
            // 填充类型
            var fillTypes = new string[] { "纯色填充", "渐变填充", "图案填充", "纹理填充", "图片填充", "无填充" };
            _cmbFillType?.Items.AddRange(fillTypes);
            if (_cmbFillType != null)
                _cmbFillType.SelectedItem = "纯色填充";

            // 边框样式
            var borderStyles = new string[] { "实线", "虚线", "点线", "双线", "粗线", "细线" };
            _cmbBorderStyle?.Items.AddRange(borderStyles);
            if (_cmbBorderStyle != null)
                _cmbBorderStyle.SelectedItem = "实线";

            // 渐变类型
            var gradientTypes = new string[] { "线性渐变", "径向渐变", "角度渐变", "反射渐变" };
            _cmbGradientType?.Items.AddRange(gradientTypes);
            if (_cmbGradientType != null)
                _cmbGradientType.SelectedItem = "线性渐变";

            // 阴影类型
            var shadowTypes = new string[] { "外阴影", "内阴影", "透视阴影", "反射阴影" };
            _cmbShadowType?.Items.AddRange(shadowTypes);
            if (_cmbShadowType != null)
                _cmbShadowType.SelectedItem = "外阴影";

            // 特殊效果
            var effectTypes = new string[] { "无效果", "发光", "柔化边缘", "反射", "棱台", "浮雕" };
            _cmbEffectType?.Items.AddRange(effectTypes);
            if (_cmbEffectType != null)
                _cmbEffectType.SelectedItem = "无效果";

            // 设置初始状态
            UpdateControlStates();

            // 设置控件文字对齐
            SetControlsTextAlignment();
        }

        /// <summary>
        /// 设置控件文字对齐
        /// </summary>
        private void SetControlsTextAlignment()
        {
            // 设置数值输入框文字居中
            SetNumericUpDownTextAlign(_nudTransparency);
            SetNumericUpDownTextAlign(_nudGradientAngle);
            SetNumericUpDownTextAlign(_nudBorderWidth);
            SetNumericUpDownTextAlign(_nudShadowOffset);

            // 设置下拉框文字居中
            SetComboBoxTextAlign(_cmbFillType);
            SetComboBoxTextAlign(_cmbGradientType);
            SetComboBoxTextAlign(_cmbBorderStyle);
            SetComboBoxTextAlign(_cmbShadowType);
            SetComboBoxTextAlign(_cmbEffectType);
        }

        /// <summary>
        /// 设置NumericUpDown文字居中显示
        /// </summary>
        private static void SetNumericUpDownTextAlign(NumericUpDown? numericUpDown)
        {
            if (numericUpDown != null)
                numericUpDown.TextAlign = HorizontalAlignment.Center;
        }

        /// <summary>
        /// 设置ComboBox文字居中显示
        /// </summary>
        private static void SetComboBoxTextAlign(ComboBox? comboBox)
        {
            if (comboBox == null) return;

            comboBox.DrawMode = DrawMode.OwnerDrawFixed;
            comboBox.DrawItem += (sender, e) =>
            {
                if (e.Index < 0) return;

                e.DrawBackground();

                var text = comboBox.Items[e.Index].ToString();
                if (!string.IsNullOrEmpty(text))
                {
                    var textBounds = e.Bounds;
                    var textFlags = TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter;
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor, textFlags);
                }

                e.DrawFocusRectangle();
            };
        }

        /// <summary>
        /// 更新控件状态
        /// </summary>
        private void UpdateControlStates()
        {
            // 渐变相关控件状态
            bool gradientEnabled = _chkGradientFill?.Checked ?? false;
            if (_cmbGradientType != null) _cmbGradientType.Enabled = gradientEnabled;
            if (_btnGradientColor1 != null) _btnGradientColor1.Enabled = gradientEnabled;
            if (_btnGradientColor2 != null) _btnGradientColor2.Enabled = gradientEnabled;
            if (_nudGradientAngle != null) _nudGradientAngle.Enabled = gradientEnabled;

            // 边框相关控件状态
            bool borderEnabled = _chkShowBorder?.Checked ?? false;
            if (_cmbBorderStyle != null) _cmbBorderStyle.Enabled = borderEnabled;
            if (_btnBorderColor != null) _btnBorderColor.Enabled = borderEnabled;
            if (_nudBorderWidth != null) _nudBorderWidth.Enabled = borderEnabled;

            // 阴影相关控件状态
            bool shadowEnabled = _chkShowShadow?.Checked ?? false;
            if (_cmbShadowType != null) _cmbShadowType.Enabled = shadowEnabled;
            if (_btnShadowColor != null) _btnShadowColor.Enabled = shadowEnabled;
            if (_nudShadowOffset != null) _nudShadowOffset.Enabled = shadowEnabled;
        }

        #endregion

        #region 事件处理方法

        /// <summary>
        /// 填充颜色按钮点击事件
        /// </summary>
        private void BtnFillColor_Click(object? sender, EventArgs e)
        {
            using var colorDialog = new ColorDialog { Color = _fillColor };
            if (colorDialog.ShowDialog() == DialogResult.OK)
            {
                _fillColor = colorDialog.Color;
                if (_btnFillColor != null)
                    _btnFillColor.BackColor = _fillColor;
            }
        }

        /// <summary>
        /// 边框颜色按钮点击事件
        /// </summary>
        private void BtnBorderColor_Click(object? sender, EventArgs e)
        {
            using var colorDialog = new ColorDialog { Color = _borderColor };
            if (colorDialog.ShowDialog() == DialogResult.OK)
            {
                _borderColor = colorDialog.Color;
                if (_btnBorderColor != null)
                    _btnBorderColor.BackColor = _borderColor;
            }
        }

        /// <summary>
        /// 阴影颜色按钮点击事件
        /// </summary>
        private void BtnShadowColor_Click(object? sender, EventArgs e)
        {
            using var colorDialog = new ColorDialog { Color = _shadowColor };
            if (colorDialog.ShowDialog() == DialogResult.OK)
            {
                _shadowColor = colorDialog.Color;
                if (_btnShadowColor != null)
                    _btnShadowColor.BackColor = _shadowColor;
            }
        }

        /// <summary>
        /// 渐变颜色1按钮点击事件
        /// </summary>
        private void BtnGradientColor1_Click(object? sender, EventArgs e)
        {
            using var colorDialog = new ColorDialog { Color = _gradientColor1 };
            if (colorDialog.ShowDialog() == DialogResult.OK)
            {
                _gradientColor1 = colorDialog.Color;
                if (_btnGradientColor1 != null)
                    _btnGradientColor1.BackColor = _gradientColor1;
            }
        }

        /// <summary>
        /// 渐变颜色2按钮点击事件
        /// </summary>
        private void BtnGradientColor2_Click(object? sender, EventArgs e)
        {
            using var colorDialog = new ColorDialog { Color = _gradientColor2 };
            if (colorDialog.ShowDialog() == DialogResult.OK)
            {
                _gradientColor2 = colorDialog.Color;
                if (_btnGradientColor2 != null)
                    _btnGradientColor2.BackColor = _gradientColor2;
            }
        }

        /// <summary>
        /// 渐变填充复选框状态改变事件
        /// </summary>
        private void ChkGradientFill_CheckedChanged(object? sender, EventArgs e)
        {
            UpdateControlStates();
        }

        /// <summary>
        /// 显示边框复选框状态改变事件
        /// </summary>
        private void ChkShowBorder_CheckedChanged(object? sender, EventArgs e)
        {
            UpdateControlStates();
        }

        /// <summary>
        /// 显示阴影复选框状态改变事件
        /// </summary>
        private void ChkShowShadow_CheckedChanged(object? sender, EventArgs e)
        {
            UpdateControlStates();
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void BtnOK_Click(object? sender, EventArgs e)
        {
            try
            {
                // 收集设置
                Settings = new ShapeStyleSettings
                {
                    FillType = _cmbFillType?.SelectedItem?.ToString() ?? "纯色填充",
                    FillColor = _fillColor,
                    Transparency = (int)(_nudTransparency?.Value ?? 0),
                    UseGradientFill = _chkGradientFill?.Checked ?? false,
                    GradientType = _cmbGradientType?.SelectedItem?.ToString() ?? "线性渐变",
                    GradientColor1 = _gradientColor1,
                    GradientColor2 = _gradientColor2,
                    GradientAngle = (int)(_nudGradientAngle?.Value ?? 0),
                    ShowBorder = _chkShowBorder?.Checked ?? false,
                    BorderStyle = _cmbBorderStyle?.SelectedItem?.ToString() ?? "实线",
                    BorderColor = _borderColor,
                    BorderWidth = (int)(_nudBorderWidth?.Value ?? 2),
                    ShowShadow = _chkShowShadow?.Checked ?? false,
                    ShadowType = _cmbShadowType?.SelectedItem?.ToString() ?? "外阴影",
                    ShadowColor = _shadowColor,
                    ShadowOffset = (int)(_nudShadowOffset?.Value ?? 5),
                    Show3D = _chkShow3D?.Checked ?? false,
                    EffectType = _cmbEffectType?.SelectedItem?.ToString() ?? "无效果"
                };

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存设置时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void BtnCancel_Click(object? sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// 重置按钮点击事件
        /// </summary>
        private void BtnReset_Click(object? sender, EventArgs e)
        {
            LoadDefaultValues();
        }

        /// <summary>
        /// 预览按钮点击事件
        /// </summary>
        private void BtnPreview_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("预览功能将显示应用当前设置后的形状效果。\n\n" +
                           "此功能将在后续版本中实现。", "预览",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        #endregion
    }

    /// <summary>
    /// 形状样式设置类
    /// </summary>
    public class ShapeStyleSettings
    {
        public string FillType { get; set; } = "纯色填充";
        public Color FillColor { get; set; } = Color.LightBlue;
        public int Transparency { get; set; } = 0;
        public bool UseGradientFill { get; set; } = false;
        public string GradientType { get; set; } = "线性渐变";
        public Color GradientColor1 { get; set; } = Color.White;
        public Color GradientColor2 { get; set; } = Color.Blue;
        public int GradientAngle { get; set; } = 0;
        public bool ShowBorder { get; set; } = false;
        public string BorderStyle { get; set; } = "实线";
        public Color BorderColor { get; set; } = Color.Black;
        public int BorderWidth { get; set; } = 2;
        public bool ShowShadow { get; set; } = false;
        public string ShadowType { get; set; } = "外阴影";
        public Color ShadowColor { get; set; } = Color.Gray;
        public int ShadowOffset { get; set; } = 5;
        public bool Show3D { get; set; } = false;
        public string EffectType { get; set; } = "无效果";
    }
}
