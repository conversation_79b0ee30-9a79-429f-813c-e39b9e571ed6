using System;
using System.Drawing;
using System.Windows.Forms;
using PPTPiliangChuli.Models;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 文件名替换规则编辑窗体 - 用于添加和编辑文件名模式替换规则，支持文本替换和正则匹配两种模式
    /// </summary>
    public partial class FilenamePatternReplacementRuleForm : Form
    {
        private FilenamePatternReplacementRule _rule;
        private bool _isEditMode;

        // 控件
        private TextBox txtRuleName = null!;
        private ComboBox cmbMatchType = null!;
        private TextBox txtSourceText = null!;
        private TextBox txtTargetText = null!;
        private CheckBox chkCaseSensitive = null!;
        private CheckBox chkIncludeExtension = null!;
        private CheckBox chkIsEnabled = null!;
        private Button btnOK = null!;
        private Button btnCancel = null!;

        public FilenamePatternReplacementRule Rule => _rule;

        /// <summary>
        /// 构造函数 - 新增模式
        /// </summary>
        public FilenamePatternReplacementRuleForm()
        {
            _rule = new FilenamePatternReplacementRule();
            _isEditMode = false;
            InitializeComponent();
            InitializeControls();
        }

        /// <summary>
        /// 构造函数 - 编辑模式
        /// </summary>
        public FilenamePatternReplacementRuleForm(FilenamePatternReplacementRule rule)
        {
            _rule = rule ?? new FilenamePatternReplacementRule();
            _isEditMode = true;
            InitializeComponent();
            InitializeControls();
            LoadRuleData();
        }

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponent()
        {
            this.Text = _isEditMode ? "编辑文件名替换规则" : "添加文件名替换规则";
            this.Size = new Size(800, 550);  // 进一步增加窗口高度以提供更多垂直间距
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Font = new Font("Microsoft YaHei UI", 10F);  // 字体不少于10F
        }

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void InitializeControls()
        {
            int yPos = 50;  // 进一步增加起始位置，提供更多顶部空间
            int labelWidth = 140;  // 增加标签宽度
            int controlWidth = 580;  // 大幅增加控件宽度以适应新窗体
            int rowHeight = 75;  // 进一步增加行高，提供更宽松的垂直间距
            int hintSpacing = 8;  // 提示文字与输入框的间距

            // 规则名称
            var lblRuleName = new Label
            {
                Text = "规则名称:",
                Location = new Point(30, yPos + 8),
                Size = new Size(labelWidth, 35),
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font("Microsoft YaHei UI", 11F)  // 增大字体到11F
            };
            this.Controls.Add(lblRuleName);

            txtRuleName = new TextBox
            {
                Location = new Point(180, yPos),
                Size = new Size(controlWidth, 35),
                Font = new Font("Microsoft YaHei UI", 10F),  // 字体不少于10F
                TextAlign = HorizontalAlignment.Center  // 输入框文字居中显示
            };
            this.Controls.Add(txtRuleName);
            yPos += rowHeight;

            // 匹配类型
            var lblMatchType = new Label
            {
                Text = "匹配类型:",
                Location = new Point(30, yPos + 8),
                Size = new Size(labelWidth, 35),
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font("Microsoft YaHei UI", 11F)  // 增大字体到11F
            };
            this.Controls.Add(lblMatchType);

            cmbMatchType = new ComboBox
            {
                Location = new Point(180, yPos),
                Size = new Size(300, 35),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 10F),  // 字体不少于10F
                DrawMode = DrawMode.OwnerDrawFixed  // 启用自定义绘制以实现文字居中
            };
            cmbMatchType.Items.AddRange(new[] { "文本替换", "正则匹配" });
            cmbMatchType.SelectedIndex = 0;

            // 添加自定义绘制事件以实现下拉框文字居中显示
            cmbMatchType.DrawItem += (sender, e) =>
            {
                if (e.Index < 0) return;

                e.DrawBackground();

                var text = cmbMatchType.Items[e.Index].ToString();
                var textBounds = e.Bounds;
                var textFlags = TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter;

                TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor, textFlags);

                e.DrawFocusRectangle();
            };

            this.Controls.Add(cmbMatchType);
            yPos += rowHeight;

            // 源文本
            var lblSourceText = new Label
            {
                Text = "源文本:",
                Location = new Point(30, yPos + 8),
                Size = new Size(labelWidth, 35),
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font("Microsoft YaHei UI", 11F)  // 增大字体到11F
            };
            this.Controls.Add(lblSourceText);

            txtSourceText = new TextBox
            {
                Location = new Point(180, yPos),
                Size = new Size(controlWidth, 35),
                Font = new Font("Microsoft YaHei UI", 10F),  // 字体不少于10F
                PlaceholderText = "要替换的文本或正则表达式",
                TextAlign = HorizontalAlignment.Center  // 输入框文字居中显示
            };
            this.Controls.Add(txtSourceText);

            // 源文本说明 - 使用独立的间距控制
            var lblSourceHint = new Label
            {
                Text = "提示：文本替换支持通配符(*,?)，正则匹配支持正则表达式语法",
                Location = new Point(180, yPos + 35 + hintSpacing),
                Size = new Size(controlWidth, 25),
                ForeColor = Color.Gray,
                Font = new Font("Microsoft YaHei UI", 10F)  // 提示文字也使用10F
            };
            this.Controls.Add(lblSourceHint);
            yPos += rowHeight;

            // 目标文本
            var lblTargetText = new Label
            {
                Text = "目标文本:",
                Location = new Point(30, yPos + 8),
                Size = new Size(labelWidth, 35),
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font("Microsoft YaHei UI", 11F)  // 增大字体到11F
            };
            this.Controls.Add(lblTargetText);

            txtTargetText = new TextBox
            {
                Location = new Point(180, yPos),
                Size = new Size(controlWidth, 35),
                Font = new Font("Microsoft YaHei UI", 10F),  // 字体不少于10F
                PlaceholderText = "替换后的文本",
                TextAlign = HorizontalAlignment.Center  // 输入框文字居中显示
            };
            this.Controls.Add(txtTargetText);

            // 目标文本说明 - 使用独立的间距控制
            var lblTargetHint = new Label
            {
                Text = "提示：正则匹配可使用$1、$2等引用捕获组，文本替换直接输入替换内容",
                Location = new Point(180, yPos + 35 + hintSpacing),
                Size = new Size(controlWidth, 25),
                ForeColor = Color.Gray,
                Font = new Font("Microsoft YaHei UI", 10F)  // 提示文字也使用10F
            };
            this.Controls.Add(lblTargetHint);
            yPos += rowHeight;

            // 选项 - 单独一行，两个复选框并排，增加间距
            chkCaseSensitive = new CheckBox
            {
                Text = "区分大小写",
                Location = new Point(180, yPos),
                Size = new Size(160, 35),
                UseVisualStyleBackColor = true,
                Font = new Font("Microsoft YaHei UI", 10F)  // 字体不少于10F
            };
            this.Controls.Add(chkCaseSensitive);

            chkIncludeExtension = new CheckBox
            {
                Text = "包含文件扩展名",
                Location = new Point(380, yPos),
                Size = new Size(200, 35),
                UseVisualStyleBackColor = true,
                Font = new Font("Microsoft YaHei UI", 10F)  // 字体不少于10F
            };
            this.Controls.Add(chkIncludeExtension);
            yPos += rowHeight;

            // 启用状态 - 增加额外的上间距
            yPos += 10;  // 为启用状态复选框增加额外间距
            chkIsEnabled = new CheckBox
            {
                Text = "启用此规则",
                Location = new Point(30, yPos),
                Size = new Size(160, 35),
                UseVisualStyleBackColor = true,
                Checked = true,
                Font = new Font("Microsoft YaHei UI", 10F)  // 字体不少于10F
            };
            this.Controls.Add(chkIsEnabled);

            // 按钮区域 - 使用固定位置确保可见
            int buttonY = 430;  // 固定按钮Y位置，确保在550px高度窗体内可见
            btnOK = new Button
            {
                Text = "确定",
                Location = new Point(500, buttonY),
                Size = new Size(100, 40),
                DialogResult = DialogResult.OK,
                UseVisualStyleBackColor = true,
                Font = new Font("Microsoft YaHei UI", 10F)  // 字体不少于10F
            };
            btnOK.Click += BtnOK_Click;
            this.Controls.Add(btnOK);

            btnCancel = new Button
            {
                Text = "取消",
                Location = new Point(610, buttonY),
                Size = new Size(100, 40),
                DialogResult = DialogResult.Cancel,
                UseVisualStyleBackColor = true,
                Font = new Font("Microsoft YaHei UI", 10F)  // 字体不少于10F
            };
            this.Controls.Add(btnCancel);

            // 设置默认按钮
            this.AcceptButton = btnOK;
            this.CancelButton = btnCancel;
        }

        /// <summary>
        /// 加载规则数据
        /// </summary>
        private void LoadRuleData()
        {
            txtRuleName.Text = _rule.RuleName;
            // 将原来的匹配类型映射到简化的类型
            cmbMatchType.SelectedIndex = _rule.MatchType == FilenameMatchType.Regex ? 1 : 0;
            txtSourceText.Text = _rule.SourcePattern;
            txtTargetText.Text = _rule.TargetPattern;
            chkCaseSensitive.Checked = _rule.CaseSensitive;
            chkIncludeExtension.Checked = _rule.IncludeExtension;
            chkIsEnabled.Checked = _rule.IsEnabled;
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void BtnOK_Click(object? sender, EventArgs e)
        {
            try
            {
                // 验证输入
                if (string.IsNullOrWhiteSpace(txtRuleName.Text))
                {
                    MessageBox.Show("请输入规则名称", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtRuleName.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtSourceText.Text))
                {
                    MessageBox.Show("请输入源文本", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtSourceText.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtTargetText.Text))
                {
                    MessageBox.Show("请输入目标文本", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtTargetText.Focus();
                    return;
                }

                // 创建临时规则进行验证
                var tempRule = new FilenamePatternReplacementRule
                {
                    RuleName = txtRuleName.Text.Trim(),
                    MatchType = cmbMatchType.SelectedIndex == 1 ? FilenameMatchType.Regex : FilenameMatchType.Wildcard,
                    SourcePattern = txtSourceText.Text.Trim(),
                    TargetPattern = txtTargetText.Text.Trim(),
                    CaseSensitive = chkCaseSensitive.Checked,
                    IncludeExtension = chkIncludeExtension.Checked,
                    IsEnabled = chkIsEnabled.Checked
                };

                // 使用服务验证规则
                var filenameService = new Services.FilenameReplacementService();
                var validationResult = filenameService.ValidateRule(tempRule);

                if (!validationResult.IsValid)
                {
                    MessageBox.Show($"规则验证失败: {validationResult.ErrorMessage}", "验证失败",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 保存数据
                _rule.RuleName = tempRule.RuleName;
                _rule.MatchType = tempRule.MatchType;
                _rule.SourcePattern = tempRule.SourcePattern;
                _rule.TargetPattern = tempRule.TargetPattern;
                _rule.CaseSensitive = tempRule.CaseSensitive;
                _rule.IncludeExtension = tempRule.IncludeExtension;
                _rule.IsEnabled = tempRule.IsEnabled;

                if (!_isEditMode)
                {
                    _rule.CreatedTime = DateTime.Now;
                }

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存规则时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
