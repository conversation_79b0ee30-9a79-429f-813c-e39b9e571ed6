namespace PPTPiliangChuli.Forms
{
    partial class ContentDeletionForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ContentDeletionForm));
            tabControlMain = new TabControl();
            tabPageDocumentDeletion = new TabPage();
            tabPageContentRemoval = new TabPage();
            tabPageTextDeletion = new TabPage();
            tabPageImageDeletion = new TabPage();
            tabPageTableDeletion = new TabPage();
            tabPageChartDeletion = new TabPage();
            tabPageMediaDeletion = new TabPage();
            tabPageContactDeletion = new TabPage();
            tabPageAnimationDeletion = new TabPage();
            tabPageNotesDeletion = new TabPage();
            tabPageFormatDeletion = new TabPage();
            panelButtons = new Panel();
            btnReset = new Button();
            btnApply = new Button();
            btnCancel = new Button();
            btnOK = new Button();
            tabControlMain.SuspendLayout();
            panelButtons.SuspendLayout();
            SuspendLayout();
            // 
            // tabControlMain
            // 
            tabControlMain.Controls.Add(tabPageDocumentDeletion);
            tabControlMain.Controls.Add(tabPageContentRemoval);
            tabControlMain.Controls.Add(tabPageTextDeletion);
            tabControlMain.Controls.Add(tabPageImageDeletion);
            tabControlMain.Controls.Add(tabPageTableDeletion);
            tabControlMain.Controls.Add(tabPageChartDeletion);
            tabControlMain.Controls.Add(tabPageMediaDeletion);
            tabControlMain.Controls.Add(tabPageContactDeletion);
            tabControlMain.Controls.Add(tabPageAnimationDeletion);
            tabControlMain.Controls.Add(tabPageNotesDeletion);
            tabControlMain.Controls.Add(tabPageFormatDeletion);
            tabControlMain.Dock = DockStyle.Fill;
            tabControlMain.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            tabControlMain.ItemSize = new Size(105, 40);
            tabControlMain.Location = new Point(20, 20);
            tabControlMain.Multiline = true;
            tabControlMain.Name = "tabControlMain";
            tabControlMain.SelectedIndex = 0;
            tabControlMain.Size = new Size(1160, 720);
            tabControlMain.SizeMode = TabSizeMode.Fixed;
            tabControlMain.TabIndex = 0;
            // 
            // tabPageDocumentDeletion
            // 
            tabPageDocumentDeletion.Location = new Point(4, 44);
            tabPageDocumentDeletion.Name = "tabPageDocumentDeletion";
            tabPageDocumentDeletion.Padding = new Padding(20);
            tabPageDocumentDeletion.Size = new Size(1152, 672);
            tabPageDocumentDeletion.TabIndex = 0;
            tabPageDocumentDeletion.Text = "删除文档";
            tabPageDocumentDeletion.UseVisualStyleBackColor = true;
            // 
            // tabPageContentRemoval
            // 
            tabPageContentRemoval.Location = new Point(4, 44);
            tabPageContentRemoval.Name = "tabPageContentRemoval";
            tabPageContentRemoval.Padding = new Padding(20);
            tabPageContentRemoval.Size = new Size(1152, 672);
            tabPageContentRemoval.TabIndex = 1;
            tabPageContentRemoval.Text = "内容删除设置";
            tabPageContentRemoval.UseVisualStyleBackColor = true;
            // 
            // tabPageTextDeletion
            // 
            tabPageTextDeletion.Location = new Point(4, 44);
            tabPageTextDeletion.Name = "tabPageTextDeletion";
            tabPageTextDeletion.Padding = new Padding(20);
            tabPageTextDeletion.Size = new Size(1152, 672);
            tabPageTextDeletion.TabIndex = 2;
            tabPageTextDeletion.Text = "文本删除";
            tabPageTextDeletion.UseVisualStyleBackColor = true;
            // 
            // tabPageImageDeletion
            // 
            tabPageImageDeletion.Location = new Point(4, 44);
            tabPageImageDeletion.Name = "tabPageImageDeletion";
            tabPageImageDeletion.Padding = new Padding(20);
            tabPageImageDeletion.Size = new Size(1152, 672);
            tabPageImageDeletion.TabIndex = 3;
            tabPageImageDeletion.Text = "删除图片";
            tabPageImageDeletion.UseVisualStyleBackColor = true;
            // 
            // tabPageTableDeletion
            // 
            tabPageTableDeletion.Location = new Point(4, 44);
            tabPageTableDeletion.Name = "tabPageTableDeletion";
            tabPageTableDeletion.Padding = new Padding(20);
            tabPageTableDeletion.Size = new Size(1152, 672);
            tabPageTableDeletion.TabIndex = 4;
            tabPageTableDeletion.Text = "删除表格";
            tabPageTableDeletion.UseVisualStyleBackColor = true;
            // 
            // tabPageChartDeletion
            // 
            tabPageChartDeletion.Location = new Point(4, 44);
            tabPageChartDeletion.Name = "tabPageChartDeletion";
            tabPageChartDeletion.Padding = new Padding(20);
            tabPageChartDeletion.Size = new Size(1152, 672);
            tabPageChartDeletion.TabIndex = 5;
            tabPageChartDeletion.Text = "删除图表";
            tabPageChartDeletion.UseVisualStyleBackColor = true;
            // 
            // tabPageMediaDeletion
            // 
            tabPageMediaDeletion.Location = new Point(4, 44);
            tabPageMediaDeletion.Name = "tabPageMediaDeletion";
            tabPageMediaDeletion.Padding = new Padding(20);
            tabPageMediaDeletion.Size = new Size(1152, 672);
            tabPageMediaDeletion.TabIndex = 6;
            tabPageMediaDeletion.Text = "删除音频视频";
            tabPageMediaDeletion.UseVisualStyleBackColor = true;
            // 
            // tabPageContactDeletion
            // 
            tabPageContactDeletion.Location = new Point(4, 44);
            tabPageContactDeletion.Name = "tabPageContactDeletion";
            tabPageContactDeletion.Padding = new Padding(20);
            tabPageContactDeletion.Size = new Size(1152, 672);
            tabPageContactDeletion.TabIndex = 7;
            tabPageContactDeletion.Text = "删除联系方式";
            tabPageContactDeletion.UseVisualStyleBackColor = true;
            // 
            // tabPageAnimationDeletion
            // 
            tabPageAnimationDeletion.Location = new Point(4, 44);
            tabPageAnimationDeletion.Name = "tabPageAnimationDeletion";
            tabPageAnimationDeletion.Padding = new Padding(20);
            tabPageAnimationDeletion.Size = new Size(1152, 672);
            tabPageAnimationDeletion.TabIndex = 8;
            tabPageAnimationDeletion.Text = "动画删除";
            tabPageAnimationDeletion.UseVisualStyleBackColor = true;
            // 
            // tabPageNotesDeletion
            // 
            tabPageNotesDeletion.Location = new Point(4, 44);
            tabPageNotesDeletion.Name = "tabPageNotesDeletion";
            tabPageNotesDeletion.Padding = new Padding(20);
            tabPageNotesDeletion.Size = new Size(1152, 672);
            tabPageNotesDeletion.TabIndex = 9;
            tabPageNotesDeletion.Text = "备注删除";
            tabPageNotesDeletion.UseVisualStyleBackColor = true;
            // 
            // tabPageFormatDeletion
            // 
            tabPageFormatDeletion.Location = new Point(4, 44);
            tabPageFormatDeletion.Name = "tabPageFormatDeletion";
            tabPageFormatDeletion.Padding = new Padding(20);
            tabPageFormatDeletion.Size = new Size(1152, 672);
            tabPageFormatDeletion.TabIndex = 10;
            tabPageFormatDeletion.Text = "删除全文格式";
            tabPageFormatDeletion.UseVisualStyleBackColor = true;
            // 
            // panelButtons
            // 
            panelButtons.Controls.Add(btnReset);
            panelButtons.Controls.Add(btnApply);
            panelButtons.Controls.Add(btnCancel);
            panelButtons.Controls.Add(btnOK);
            panelButtons.Dock = DockStyle.Bottom;
            panelButtons.Location = new Point(20, 740);
            panelButtons.Name = "panelButtons";
            panelButtons.Size = new Size(1160, 70);
            panelButtons.TabIndex = 1;
            // 
            // btnReset
            // 
            btnReset.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnReset.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            btnReset.Location = new Point(720, 15);
            btnReset.Name = "btnReset";
            btnReset.Size = new Size(100, 45);
            btnReset.TabIndex = 3;
            btnReset.Text = "重置";
            btnReset.UseVisualStyleBackColor = true;
            // 
            // btnApply
            // 
            btnApply.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnApply.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            btnApply.Location = new Point(830, 15);
            btnApply.Name = "btnApply";
            btnApply.Size = new Size(100, 45);
            btnApply.TabIndex = 2;
            btnApply.Text = "应用";
            btnApply.UseVisualStyleBackColor = true;
            // 
            // btnCancel
            // 
            btnCancel.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnCancel.DialogResult = DialogResult.Cancel;
            btnCancel.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            btnCancel.Location = new Point(1050, 15);
            btnCancel.Name = "btnCancel";
            btnCancel.Size = new Size(100, 45);
            btnCancel.TabIndex = 1;
            btnCancel.Text = "取消";
            btnCancel.UseVisualStyleBackColor = true;
            // 
            // btnOK
            // 
            btnOK.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnOK.DialogResult = DialogResult.OK;
            btnOK.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            btnOK.Location = new Point(940, 15);
            btnOK.Name = "btnOK";
            btnOK.Size = new Size(100, 45);
            btnOK.TabIndex = 0;
            btnOK.Text = "确定";
            btnOK.UseVisualStyleBackColor = true;
            // 
            // ContentDeletionForm
            // 
            AutoScaleDimensions = new SizeF(8F, 19F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1200, 830);
            Controls.Add(tabControlMain);
            Controls.Add(panelButtons);
            Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            Icon = (Icon?)resources.GetObject("$this.Icon");
            MaximizeBox = false;
            MinimizeBox = false;
            MinimumSize = new Size(1200, 830);
            Name = "ContentDeletionForm";
            Padding = new Padding(20);
            ShowIcon = true;
            ShowInTaskbar = false;
            StartPosition = FormStartPosition.CenterParent;
            Text = "内容删除设置";
            tabControlMain.ResumeLayout(false);
            panelButtons.ResumeLayout(false);
            ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TabControl tabControlMain;
        private System.Windows.Forms.TabPage tabPageDocumentDeletion;
        private System.Windows.Forms.TabPage tabPageContentRemoval;
        private System.Windows.Forms.TabPage tabPageTextDeletion;
        private System.Windows.Forms.TabPage tabPageImageDeletion;
        private System.Windows.Forms.TabPage tabPageTableDeletion;
        private System.Windows.Forms.TabPage tabPageChartDeletion;
        private System.Windows.Forms.TabPage tabPageMediaDeletion;
        private System.Windows.Forms.TabPage tabPageContactDeletion;
        private System.Windows.Forms.TabPage tabPageAnimationDeletion;
        private System.Windows.Forms.TabPage tabPageNotesDeletion;
        private System.Windows.Forms.TabPage tabPageFormatDeletion;
        private System.Windows.Forms.Panel panelButtons;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Button btnApply;
        private System.Windows.Forms.Button btnReset;
    }
}
