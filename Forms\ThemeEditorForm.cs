using System;
using System.Drawing;
using System.Windows.Forms;
using System.Collections.Generic;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 主题编辑器窗体
    /// </summary>
    public partial class ThemeEditorForm : Form
    {
        #region 私有字段

        /// <summary>
        /// 创建的主题信息
        /// </summary>
        public CustomTheme? CreatedTheme { get; private set; }

        /// <summary>
        /// 是否为编辑模式
        /// </summary>
        private readonly bool _isEditMode;

        /// <summary>
        /// 编辑的主题名称
        /// </summary>
        private readonly string _editThemeName;

        #region 控件字段
        private TextBox? _txtThemeName;
        private TextBox? _txtDescription;
        private Button? _btnPrimaryColor, _btnSecondaryColor, _btnAccentColor1, _btnAccentColor2;
        private Button? _btnAccentColor3, _btnAccentColor4, _btnAccentColor5, _btnAccentColor6;
        private ComboBox? _cmbTitleFont, _cmbBodyFont;
        private Button? _btnPreview, _btnOK, _btnCancel;
        private Panel? _pnlPreview;

        // 颜色存储
        private Color _primaryColor = Color.FromArgb(68, 114, 196);
        private Color _secondaryColor = Color.FromArgb(255, 255, 255);
        private Color _accentColor1 = Color.FromArgb(91, 155, 213);
        private Color _accentColor2 = Color.FromArgb(237, 125, 49);
        private Color _accentColor3 = Color.FromArgb(165, 165, 165);
        private Color _accentColor4 = Color.FromArgb(255, 192, 0);
        private Color _accentColor5 = Color.FromArgb(68, 114, 196);
        private Color _accentColor6 = Color.FromArgb(112, 173, 71);
        #endregion

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数 - 创建新主题
        /// </summary>
        public ThemeEditorForm()
        {
            _isEditMode = false;
            _editThemeName = "";
            InitializeComponent();
            InitializeControls();
            LoadDefaultValues();
        }

        /// <summary>
        /// 构造函数 - 编辑现有主题
        /// </summary>
        /// <param name="themeName">要编辑的主题名称</param>
        public ThemeEditorForm(string themeName)
        {
            _isEditMode = true;
            _editThemeName = themeName;
            InitializeComponent();
            InitializeControls();
            LoadThemeForEdit(themeName);
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponent()
        {
            this.Text = _isEditMode ? "编辑主题" : "创建新主题";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Font = new Font("Microsoft YaHei UI", 9F);
        }

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void InitializeControls()
        {
            CreateBasicInfoGroup();
            CreateColorSchemeGroup();
            CreateFontSchemeGroup();
            CreatePreviewGroup();
            CreateActionButtons();
        }

        /// <summary>
        /// 创建基本信息组
        /// </summary>
        private void CreateBasicInfoGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "基本信息",
                Location = new Point(20, 20),
                Size = new Size(550, 80),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            var lblName = new Label
            {
                Text = "主题名称:",
                Location = new Point(20, 25),
                Size = new Size(80, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _txtThemeName = new TextBox
            {
                Location = new Point(110, 23),
                Size = new Size(200, 23),
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            var lblDesc = new Label
            {
                Text = "描述:",
                Location = new Point(330, 25),
                Size = new Size(50, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _txtDescription = new TextBox
            {
                Location = new Point(390, 23),
                Size = new Size(140, 23),
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            groupBox.Controls.AddRange(new Control[] { lblName, _txtThemeName, lblDesc, _txtDescription });
            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建颜色方案组
        /// </summary>
        private void CreateColorSchemeGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "颜色方案",
                Location = new Point(20, 110),
                Size = new Size(550, 120),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 第一行颜色
            var lblPrimary = new Label { Text = "主色:", Location = new Point(20, 25), Size = new Size(50, 20) };
            _btnPrimaryColor = CreateColorButton(_primaryColor, new Point(80, 23));
            _btnPrimaryColor.Click += (s, e) => SelectColor(ref _primaryColor, _btnPrimaryColor);

            var lblSecondary = new Label { Text = "辅色:", Location = new Point(160, 25), Size = new Size(50, 20) };
            _btnSecondaryColor = CreateColorButton(_secondaryColor, new Point(220, 23));
            _btnSecondaryColor.Click += (s, e) => SelectColor(ref _secondaryColor, _btnSecondaryColor);

            var lblAccent1 = new Label { Text = "强调1:", Location = new Point(300, 25), Size = new Size(50, 20) };
            _btnAccentColor1 = CreateColorButton(_accentColor1, new Point(360, 23));
            _btnAccentColor1.Click += (s, e) => SelectColor(ref _accentColor1, _btnAccentColor1);

            var lblAccent2 = new Label { Text = "强调2:", Location = new Point(430, 25), Size = new Size(50, 20) };
            _btnAccentColor2 = CreateColorButton(_accentColor2, new Point(490, 23));
            _btnAccentColor2.Click += (s, e) => SelectColor(ref _accentColor2, _btnAccentColor2);

            // 第二行颜色
            var lblAccent3 = new Label { Text = "强调3:", Location = new Point(20, 55), Size = new Size(50, 20) };
            _btnAccentColor3 = CreateColorButton(_accentColor3, new Point(80, 53));
            _btnAccentColor3.Click += (s, e) => SelectColor(ref _accentColor3, _btnAccentColor3);

            var lblAccent4 = new Label { Text = "强调4:", Location = new Point(160, 55), Size = new Size(50, 20) };
            _btnAccentColor4 = CreateColorButton(_accentColor4, new Point(220, 53));
            _btnAccentColor4.Click += (s, e) => SelectColor(ref _accentColor4, _btnAccentColor4);

            var lblAccent5 = new Label { Text = "强调5:", Location = new Point(300, 55), Size = new Size(50, 20) };
            _btnAccentColor5 = CreateColorButton(_accentColor5, new Point(360, 53));
            _btnAccentColor5.Click += (s, e) => SelectColor(ref _accentColor5, _btnAccentColor5);

            var lblAccent6 = new Label { Text = "强调6:", Location = new Point(430, 55), Size = new Size(50, 20) };
            _btnAccentColor6 = CreateColorButton(_accentColor6, new Point(490, 53));
            _btnAccentColor6.Click += (s, e) => SelectColor(ref _accentColor6, _btnAccentColor6);

            groupBox.Controls.AddRange(new Control[] {
                lblPrimary, _btnPrimaryColor, lblSecondary, _btnSecondaryColor,
                lblAccent1, _btnAccentColor1, lblAccent2, _btnAccentColor2,
                lblAccent3, _btnAccentColor3, lblAccent4, _btnAccentColor4,
                lblAccent5, _btnAccentColor5, lblAccent6, _btnAccentColor6
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建字体方案组
        /// </summary>
        private void CreateFontSchemeGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "字体方案",
                Location = new Point(20, 240),
                Size = new Size(550, 80),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            var lblTitle = new Label
            {
                Text = "标题字体:",
                Location = new Point(20, 25),
                Size = new Size(80, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbTitleFont = new ComboBox
            {
                Location = new Point(110, 23),
                Size = new Size(150, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            var lblBody = new Label
            {
                Text = "正文字体:",
                Location = new Point(280, 25),
                Size = new Size(80, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbBodyFont = new ComboBox
            {
                Location = new Point(370, 23),
                Size = new Size(150, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            // 加载系统字体
            LoadSystemFonts();

            groupBox.Controls.AddRange(new Control[] { lblTitle, _cmbTitleFont, lblBody, _cmbBodyFont });
            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建预览组
        /// </summary>
        private void CreatePreviewGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "预览",
                Location = new Point(20, 330),
                Size = new Size(550, 80),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            _pnlPreview = new Panel
            {
                Location = new Point(20, 25),
                Size = new Size(500, 40),
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = _secondaryColor
            };

            _btnPreview = new Button
            {
                Text = "刷新预览",
                Location = new Point(450, 50),
                Size = new Size(80, 25),
                Font = new Font("Microsoft YaHei UI", 8F)
            };
            _btnPreview.Click += BtnPreview_Click;

            groupBox.Controls.AddRange(new Control[] { _pnlPreview, _btnPreview });
            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建操作按钮
        /// </summary>
        private void CreateActionButtons()
        {
            _btnOK = new Button
            {
                Text = "确定",
                Location = new Point(420, 430),
                Size = new Size(75, 30),
                DialogResult = DialogResult.OK,
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            _btnOK.Click += BtnOK_Click;

            _btnCancel = new Button
            {
                Text = "取消",
                Location = new Point(505, 430),
                Size = new Size(75, 30),
                DialogResult = DialogResult.Cancel,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            this.Controls.AddRange(new Control[] { _btnOK, _btnCancel });
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 创建颜色按钮
        /// </summary>
        /// <param name="color">颜色</param>
        /// <param name="location">位置</param>
        /// <returns>颜色按钮</returns>
        private Button CreateColorButton(Color color, Point location)
        {
            return new Button
            {
                Location = location,
                Size = new Size(30, 23),
                BackColor = color,
                ForeColor = GetContrastColor(color),
                Text = "",
                FlatStyle = FlatStyle.Flat
            };
        }

        /// <summary>
        /// 获取对比色
        /// </summary>
        /// <param name="color">背景颜色</param>
        /// <returns>对比色</returns>
        private static Color GetContrastColor(Color color)
        {
            double brightness = (color.R * 0.299 + color.G * 0.587 + color.B * 0.114) / 255;
            return brightness > 0.5 ? Color.Black : Color.White;
        }

        /// <summary>
        /// 选择颜色
        /// </summary>
        /// <param name="colorField">颜色字段</param>
        /// <param name="button">颜色按钮</param>
        private void SelectColor(ref Color colorField, Button? button)
        {
            if (button == null) return;

            using var colorDialog = new ColorDialog
            {
                Color = colorField,
                FullOpen = true
            };

            if (colorDialog.ShowDialog(this) == DialogResult.OK)
            {
                colorField = colorDialog.Color;
                button.BackColor = colorField;
                button.ForeColor = GetContrastColor(colorField);
                UpdatePreview();
            }
        }

        /// <summary>
        /// 加载系统字体
        /// </summary>
        private void LoadSystemFonts()
        {
            var commonFonts = new string[]
            {
                "Microsoft YaHei UI", "Microsoft YaHei", "SimSun", "SimHei", "KaiTi",
                "Arial", "Times New Roman", "Calibri", "Verdana", "Tahoma",
                "Georgia", "Comic Sans MS", "Impact", "Trebuchet MS"
            };

            _cmbTitleFont?.Items.AddRange(commonFonts);
            _cmbBodyFont?.Items.AddRange(commonFonts);
        }

        /// <summary>
        /// 加载默认值
        /// </summary>
        private void LoadDefaultValues()
        {
            if (_txtThemeName != null)
                _txtThemeName.Text = "新主题";

            if (_txtDescription != null)
                _txtDescription.Text = "自定义主题";

            if (_cmbTitleFont != null)
                _cmbTitleFont.SelectedItem = "Microsoft YaHei UI";

            if (_cmbBodyFont != null)
                _cmbBodyFont.SelectedItem = "Microsoft YaHei UI";

            UpdatePreview();
        }

        /// <summary>
        /// 加载主题进行编辑
        /// </summary>
        /// <param name="themeName">主题名称</param>
        private void LoadThemeForEdit(string themeName)
        {
            // 这里可以从配置文件或数据库加载主题信息
            // 目前使用默认值
            if (_txtThemeName != null)
                _txtThemeName.Text = themeName;

            if (_txtDescription != null)
                _txtDescription.Text = $"编辑 {themeName}";

            if (_cmbTitleFont != null)
                _cmbTitleFont.SelectedItem = "Microsoft YaHei UI";

            if (_cmbBodyFont != null)
                _cmbBodyFont.SelectedItem = "Microsoft YaHei UI";

            UpdatePreview();
        }

        /// <summary>
        /// 更新预览
        /// </summary>
        private void UpdatePreview()
        {
            if (_pnlPreview == null) return;

            _pnlPreview.BackColor = _secondaryColor;
            _pnlPreview.Invalidate();
        }

        #endregion

        #region 事件处理方法

        /// <summary>
        /// 预览按钮点击事件
        /// </summary>
        private void BtnPreview_Click(object? sender, EventArgs e)
        {
            UpdatePreview();
            MessageBox.Show("预览已更新！\n\n" +
                           "您可以在预览区域查看当前主题的效果。", "预览更新",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void BtnOK_Click(object? sender, EventArgs e)
        {
            try
            {
                // 验证输入
                if (string.IsNullOrWhiteSpace(_txtThemeName?.Text))
                {
                    MessageBox.Show("请输入主题名称。", "输入验证",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    _txtThemeName?.Focus();
                    return;
                }

                if (_cmbTitleFont?.SelectedItem == null || _cmbBodyFont?.SelectedItem == null)
                {
                    MessageBox.Show("请选择标题字体和正文字体。", "输入验证",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 创建主题对象
                CreatedTheme = new CustomTheme
                {
                    Name = _txtThemeName.Text.Trim(),
                    Description = _txtDescription?.Text?.Trim() ?? "",
                    PrimaryColor = _primaryColor,
                    SecondaryColor = _secondaryColor,
                    AccentColor1 = _accentColor1,
                    AccentColor2 = _accentColor2,
                    AccentColor3 = _accentColor3,
                    AccentColor4 = _accentColor4,
                    AccentColor5 = _accentColor5,
                    AccentColor6 = _accentColor6,
                    TitleFont = _cmbTitleFont.SelectedItem.ToString() ?? "",
                    BodyFont = _cmbBodyFont.SelectedItem.ToString() ?? ""
                };

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存主题时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion
    }

    /// <summary>
    /// 自定义主题信息类
    /// </summary>
    public class CustomTheme
    {
        public string Name { get; set; } = "";
        public string Description { get; set; } = "";
        public Color PrimaryColor { get; set; }
        public Color SecondaryColor { get; set; }
        public Color AccentColor1 { get; set; }
        public Color AccentColor2 { get; set; }
        public Color AccentColor3 { get; set; }
        public Color AccentColor4 { get; set; }
        public Color AccentColor5 { get; set; }
        public Color AccentColor6 { get; set; }
        public string TitleFont { get; set; } = "";
        public string BodyFont { get; set; } = "";
    }
}
