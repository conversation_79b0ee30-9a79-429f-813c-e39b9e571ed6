using System;
using System.Drawing;
using System.Windows.Forms;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 讲义母版设置窗体
    /// </summary>
    public partial class HandoutMasterSettingsForm : Form
    {
        #region 私有字段

        /// <summary>
        /// 讲义母版设置
        /// </summary>
        public HandoutMasterSettings Settings { get; private set; } = new HandoutMasterSettings();

        #region 控件字段
        private ComboBox? _cmbSlidesPerPage;
        private ComboBox? _cmbSlideArrangement;
        private NumericUpDown? _nudTopMargin, _nudBottomMargin, _nudLeftMargin, _nudRightMargin;
        private NumericUpDown? _nudSlideSpacing;
        private CheckBox? _chkShowHeader, _chkShowFooter, _chkShowPageNumber, _chkShowDate;
        private TextBox? _txtHeaderText, _txtFooterText;
        private ComboBox? _cmbDateFormat;
        private CheckBox? _chkFrameSlides;
        private Button? _btnOK, _btnCancel, _btnReset;
        #endregion

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public HandoutMasterSettingsForm()
        {
            InitializeComponent();
            InitializeControls();
            LoadDefaultValues();
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(HandoutMasterSettingsForm));
            SuspendLayout();
            // 
            // HandoutMasterSettingsForm
            // 
            ClientSize = new Size(734, 741);
            Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            Icon = (Icon)resources.GetObject("$this.Icon");
            MaximizeBox = false;
            MinimizeBox = false;
            MinimumSize = new Size(750, 780);
            Name = "HandoutMasterSettingsForm";
            StartPosition = FormStartPosition.CenterParent;
            Text = "讲义母版设置";
            ResumeLayout(false);
        }

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void InitializeControls()
        {
            CreateLayoutGroup();
            CreateMarginsGroup();
            CreateHeaderFooterGroup();
            CreateOptionsGroup();
            CreateActionButtons();
        }

        /// <summary>
        /// 创建布局组
        /// </summary>
        private void CreateLayoutGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "幻灯片布局设置",
                Location = new Point(30, 30),
                Size = new Size(690, 100),
                Font = new Font("Microsoft YaHei UI", 11F, FontStyle.Regular)
            };

            var lblPerPage = new Label
            {
                Text = "每页幻灯片数:",
                Location = new Point(30, 40),
                Size = new Size(150, 40),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbSlidesPerPage = new ComboBox
            {
                Location = new Point(190, 40),
                Size = new Size(100, 28),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 10F),
                MaxDropDownItems = 8,
                DropDownHeight = 160
            };

            var lblArrangement = new Label
            {
                Text = "排列方式:",
                Location = new Point(320, 40),
                Size = new Size(110, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbSlideArrangement = new ComboBox
            {
                Location = new Point(450, 40),
                Size = new Size(140, 40),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 10F),
                MaxDropDownItems = 6,
                DropDownHeight = 120
            };

            groupBox.Controls.AddRange(new Control[] {
                lblPerPage, _cmbSlidesPerPage, lblArrangement, _cmbSlideArrangement
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建边距组
        /// </summary>
        private void CreateMarginsGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "页面边距和间距设置",
                Location = new Point(30, 150),
                Size = new Size(690, 140),
                Font = new Font("Microsoft YaHei UI", 11F, FontStyle.Regular)
            };

            // 第一行边距
            var lblTop = new Label
            {
                Text = "上边距:",
                Location = new Point(30, 40),
                Size = new Size(100, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _nudTopMargin = new NumericUpDown
            {
                Location = new Point(140, 38),
                Size = new Size(90, 28),
                Minimum = 0,
                Maximum = 100,
                Value = 20,
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            var lblBottom = new Label
            {
                Text = "下边距:",
                Location = new Point(255, 40),
                Size = new Size(100, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _nudBottomMargin = new NumericUpDown
            {
                Location = new Point(355, 38),
                Size = new Size(80, 28),
                Minimum = 0,
                Maximum = 100,
                Value = 20,
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            var lblSpacing = new Label
            {
                Text = "幻灯片间距:",
                Location = new Point(460, 40),
                Size = new Size(130, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _nudSlideSpacing = new NumericUpDown
            {
                Location = new Point(590, 38),
                Size = new Size(80, 28),
                Minimum = 0,
                Maximum = 50,
                Value = 10,
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            // 第二行边距
            var lblLeft = new Label
            {
                Text = "左边距:",
                Location = new Point(30, 80),
                Size = new Size(90, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _nudLeftMargin = new NumericUpDown
            {
                Location = new Point(140, 78),
                Size = new Size(80, 28),
                Minimum = 0,
                Maximum = 100,
                Value = 20,
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            var lblRight = new Label
            {
                Text = "右边距:",
                Location = new Point(255, 80),
                Size = new Size(100, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _nudRightMargin = new NumericUpDown
            {
                Location = new Point(360, 78),
                Size = new Size(80, 28),
                Minimum = 0,
                Maximum = 100,
                Value = 20,
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            groupBox.Controls.AddRange(new Control[] {
                lblTop, _nudTopMargin, lblBottom, _nudBottomMargin, lblSpacing, _nudSlideSpacing,
                lblLeft, _nudLeftMargin, lblRight, _nudRightMargin
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建页眉页脚组
        /// </summary>
        private void CreateHeaderFooterGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "页眉页脚设置",
                Location = new Point(30, 310),
                Size = new Size(690, 180),
                Font = new Font("Microsoft YaHei UI", 11F, FontStyle.Regular)
            };

            // 第一行复选框
            _chkShowHeader = new CheckBox
            {
                Text = "显示页眉",
                Location = new Point(30, 35),
                Size = new Size(120, 40),
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            _chkShowFooter = new CheckBox
            {
                Text = "显示页脚",
                Location = new Point(170, 40),
                Size = new Size(120, 40),
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            _chkShowPageNumber = new CheckBox
            {
                Text = "显示页码",
                Location = new Point(310, 40),
                Size = new Size(120, 40),
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            _chkShowDate = new CheckBox
            {
                Text = "显示日期",
                Location = new Point(450, 40),
                Size = new Size(120, 40),
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            // 页眉文本
            var lblHeader = new Label
            {
                Text = "页眉文本:",
                Location = new Point(30, 80),
                Size = new Size(100, 40),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _txtHeaderText = new TextBox
            {
                Location = new Point(140, 80),
                Size = new Size(510, 40),
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            // 页脚文本
            var lblFooter = new Label
            {
                Text = "页脚文本:",
                Location = new Point(30, 125),
                Size = new Size(100, 40),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _txtFooterText = new TextBox
            {
                Location = new Point(140, 125),
                Size = new Size(300, 40),
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            var lblDateFormat = new Label
            {
                Text = "日期格式:",
                Location = new Point(460, 125),
                Size = new Size(80, 40),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbDateFormat = new ComboBox
            {
                Location = new Point(550, 125),
                Size = new Size(100, 28),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 10F),
                MaxDropDownItems = 6,
                DropDownHeight = 120
            };

            groupBox.Controls.AddRange(new Control[] {
                _chkShowHeader, _chkShowFooter, _chkShowPageNumber, _chkShowDate,
                lblHeader, _txtHeaderText, lblFooter, _txtFooterText, lblDateFormat, _cmbDateFormat
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建选项组
        /// </summary>
        private void CreateOptionsGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "其他选项设置",
                Location = new Point(30, 510),
                Size = new Size(690, 120),
                Font = new Font("Microsoft YaHei UI", 11F, FontStyle.Regular)
            };

            _chkFrameSlides = new CheckBox
            {
                Text = "为幻灯片添加边框",
                Location = new Point(30, 50),
                Size = new Size(200, 40),
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            groupBox.Controls.Add(_chkFrameSlides);
            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建操作按钮
        /// </summary>
        private void CreateActionButtons()
        {
            _btnOK = new Button
            {
                Text = "确定(&O)",
                Location = new Point(400, 655),
                Size = new Size(90, 40),
                DialogResult = DialogResult.OK,
                Font = new Font("Microsoft YaHei UI", 10F)
            };
            _btnOK.Click += BtnOK_Click;

            _btnCancel = new Button
            {
                Text = "取消(&C)",
                Location = new Point(495, 655),
                Size = new Size(90, 40),
                DialogResult = DialogResult.Cancel,
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            _btnReset = new Button
            {
                Text = "重置(&R)",
                Location = new Point(590, 655),
                Size = new Size(80, 40),
                Font = new Font("Microsoft YaHei UI", 10F)
            };
            _btnReset.Click += BtnReset_Click;

            this.Controls.AddRange(new Control[] { _btnOK, _btnCancel, _btnReset });
        }

        /// <summary>
        /// 加载默认值
        /// </summary>
        private void LoadDefaultValues()
        {
            // 加载每页幻灯片数选项
            var slidesPerPage = new string[] { "1", "2", "3", "4", "6", "9" };
            _cmbSlidesPerPage?.Items.AddRange(slidesPerPage);
            if (_cmbSlidesPerPage != null)
                _cmbSlidesPerPage.SelectedItem = "6";

            // 加载排列方式
            var arrangements = new string[] { "水平排列", "垂直排列", "网格排列" };
            _cmbSlideArrangement?.Items.AddRange(arrangements);
            if (_cmbSlideArrangement != null)
                _cmbSlideArrangement.SelectedItem = "网格排列";

            // 加载日期格式
            var dateFormats = new string[] { "短", "长", "无" };
            _cmbDateFormat?.Items.AddRange(dateFormats);
            if (_cmbDateFormat != null)
                _cmbDateFormat.SelectedItem = "短";
        }

        #endregion

        #region 事件处理方法

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void BtnOK_Click(object? sender, EventArgs e)
        {
            try
            {
                // 收集设置
                Settings = new HandoutMasterSettings
                {
                    SlidesPerPage = int.Parse(_cmbSlidesPerPage?.SelectedItem?.ToString() ?? "6"),
                    SlideArrangement = _cmbSlideArrangement?.SelectedItem?.ToString() ?? "网格排列",
                    TopMargin = (int)(_nudTopMargin?.Value ?? 20),
                    BottomMargin = (int)(_nudBottomMargin?.Value ?? 20),
                    LeftMargin = (int)(_nudLeftMargin?.Value ?? 20),
                    RightMargin = (int)(_nudRightMargin?.Value ?? 20),
                    SlideSpacing = (int)(_nudSlideSpacing?.Value ?? 10),
                    ShowHeader = _chkShowHeader?.Checked ?? false,
                    ShowFooter = _chkShowFooter?.Checked ?? false,
                    ShowPageNumber = _chkShowPageNumber?.Checked ?? false,
                    ShowDate = _chkShowDate?.Checked ?? false,
                    HeaderText = _txtHeaderText?.Text ?? "",
                    FooterText = _txtFooterText?.Text ?? "",
                    DateFormat = _cmbDateFormat?.SelectedItem?.ToString() ?? "短",
                    FrameSlides = _chkFrameSlides?.Checked ?? false
                };

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存设置时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 重置按钮点击事件
        /// </summary>
        private void BtnReset_Click(object? sender, EventArgs e)
        {
            LoadDefaultValues();
            
            // 重置数值控件
            if (_nudTopMargin != null) _nudTopMargin.Value = 20;
            if (_nudBottomMargin != null) _nudBottomMargin.Value = 20;
            if (_nudLeftMargin != null) _nudLeftMargin.Value = 20;
            if (_nudRightMargin != null) _nudRightMargin.Value = 20;
            if (_nudSlideSpacing != null) _nudSlideSpacing.Value = 10;
            
            // 重置复选框
            if (_chkShowHeader != null) _chkShowHeader.Checked = false;
            if (_chkShowFooter != null) _chkShowFooter.Checked = false;
            if (_chkShowPageNumber != null) _chkShowPageNumber.Checked = false;
            if (_chkShowDate != null) _chkShowDate.Checked = false;
            if (_chkFrameSlides != null) _chkFrameSlides.Checked = false;
            
            // 重置文本框
            if (_txtHeaderText != null) _txtHeaderText.Text = "";
            if (_txtFooterText != null) _txtFooterText.Text = "";
        }

        #endregion
    }

    /// <summary>
    /// 讲义母版设置类
    /// </summary>
    public class HandoutMasterSettings
    {
        public int SlidesPerPage { get; set; } = 6;
        public string SlideArrangement { get; set; } = "网格排列";
        public int TopMargin { get; set; } = 20;
        public int BottomMargin { get; set; } = 20;
        public int LeftMargin { get; set; } = 20;
        public int RightMargin { get; set; } = 20;
        public int SlideSpacing { get; set; } = 10;
        public bool ShowHeader { get; set; } = false;
        public bool ShowFooter { get; set; } = false;
        public bool ShowPageNumber { get; set; } = false;
        public bool ShowDate { get; set; } = false;
        public string HeaderText { get; set; } = "";
        public string FooterText { get; set; } = "";
        public string DateFormat { get; set; } = "短";
        public bool FrameSlides { get; set; } = false;
    }
}
