/// <summary>
/// 表格样式设置窗体文件
/// 用途：提供PPT文档中表格的样式设置功能，包括边框、填充、字体、主题等
/// 功能：支持表格边框样式、颜色、宽度设置，单元格填充颜色设置
/// 字体设置：支持表格文字字体、大小、颜色、样式设置
/// 主题设置：支持表格主题样式、交替行颜色、标题行样式等
/// 布局设置：支持行高、列宽、对齐方式等布局参数
/// 符合Aspose.Slides API规范，使用Table、CellFormat、TableFormat等接口
/// 作者：PPT批量处理工具
/// 创建时间：2024年
/// </summary>

using System;
using System.Drawing;
using System.Windows.Forms;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 表格样式设置窗体
    /// </summary>
    public partial class TableStyleSettingsForm : Form
    {
        #region 私有字段

        /// <summary>
        /// 表格样式设置
        /// </summary>
        public TableStyleSettings Settings { get; private set; } = new TableStyleSettings();

        #region 控件字段
        private ComboBox? _cmbBorderStyle, _cmbTableTheme;
        private NumericUpDown? _nudBorderWidth, _nudCellPaddingTop, _nudCellPaddingLeft, _nudCellPaddingRight, _nudCellPaddingBottom;
        private Button? _btnBorderColor, _btnHeaderBackColor, _btnDataBackColor, _btnAlternateBackColor;
        private CheckBox? _chkShowBorder, _chkAlternateRows, _chkHeaderRow, _chkTotalRow, _chkFirstColumn, _chkLastColumn;
        private ComboBox? _cmbHeaderFontStyle, _cmbDataFontStyle;
        private NumericUpDown? _nudHeaderFontSize, _nudDataFontSize;
        private Button? _btnHeaderFontColor, _btnDataFontColor;
        private Button? _btnOK, _btnCancel, _btnReset, _btnPreview;
        
        // 颜色存储
        private Color _borderColor = Color.Black;
        private Color _headerBackColor = Color.LightGray;
        private Color _dataBackColor = Color.White;
        private Color _alternateBackColor = Color.LightBlue;
        private Color _headerFontColor = Color.Black;
        private Color _dataFontColor = Color.Black;
        #endregion

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public TableStyleSettingsForm()
        {
            InitializeComponent();
            InitializeControls();
            LoadDefaultValues();
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(TableStyleSettingsForm));
            SuspendLayout();
            // 
            // TableStyleSettingsForm
            // 
            ClientSize = new Size(578, 544);
            Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            Icon = (Icon)resources.GetObject("$this.Icon");
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "TableStyleSettingsForm";
            StartPosition = FormStartPosition.CenterParent;
            Text = "表格样式设置";
            ResumeLayout(false);
        }

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void InitializeControls()
        {
            CreateBorderGroup();
            CreateCellGroup();
            CreateColorGroup();
            CreateFontGroup();
            CreateOptionsGroup();
            CreateActionButtons();
        }

        /// <summary>
        /// 创建边框组
        /// </summary>
        private void CreateBorderGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "边框设置",
                Location = new Point(20, 20),
                Size = new Size(550, 100),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            _chkShowBorder = new CheckBox
            {
                Text = "显示边框",
                Location = new Point(20, 25),
                Size = new Size(80, 20),
                Checked = true,
                Font = new Font("Microsoft YaHei UI", 8F)
            };

            var lblStyle = new Label
            {
                Text = "边框样式:",
                Location = new Point(120, 25),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbBorderStyle = new ComboBox
            {
                Location = new Point(200, 23),
                Size = new Size(100, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            var lblWidth = new Label
            {
                Text = "边框宽度:",
                Location = new Point(320, 25),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _nudBorderWidth = new NumericUpDown
            {
                Location = new Point(400, 23),
                Size = new Size(60, 23),
                Minimum = 1,
                Maximum = 10,
                Value = 1,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            var lblColor = new Label
            {
                Text = "边框颜色:",
                Location = new Point(20, 55),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _btnBorderColor = new Button
            {
                Location = new Point(100, 53),
                Size = new Size(50, 25),
                BackColor = _borderColor,
                FlatStyle = FlatStyle.Flat
            };
            _btnBorderColor.Click += (s, e) => SelectColor(ref _borderColor, _btnBorderColor);

            groupBox.Controls.AddRange(new Control[] {
                _chkShowBorder, lblStyle, _cmbBorderStyle, lblWidth, _nudBorderWidth, lblColor, _btnBorderColor
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建单元格组
        /// </summary>
        private void CreateCellGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "单元格设置",
                Location = new Point(20, 130),
                Size = new Size(550, 100),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            var lblPadding = new Label
            {
                Text = "内边距:",
                Location = new Point(20, 25),
                Size = new Size(60, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            var lblTop = new Label { Text = "上:", Location = new Point(90, 25), Size = new Size(20, 20) };
            _nudCellPaddingTop = new NumericUpDown
            {
                Location = new Point(115, 23),
                Size = new Size(50, 23),
                Minimum = 0,
                Maximum = 50,
                Value = 5
            };

            var lblLeft = new Label { Text = "左:", Location = new Point(175, 25), Size = new Size(20, 20) };
            _nudCellPaddingLeft = new NumericUpDown
            {
                Location = new Point(200, 23),
                Size = new Size(50, 23),
                Minimum = 0,
                Maximum = 50,
                Value = 5
            };

            var lblRight = new Label { Text = "右:", Location = new Point(260, 25), Size = new Size(20, 20) };
            _nudCellPaddingRight = new NumericUpDown
            {
                Location = new Point(285, 23),
                Size = new Size(50, 23),
                Minimum = 0,
                Maximum = 50,
                Value = 5
            };

            var lblBottom = new Label { Text = "下:", Location = new Point(345, 25), Size = new Size(20, 20) };
            _nudCellPaddingBottom = new NumericUpDown
            {
                Location = new Point(370, 23),
                Size = new Size(50, 23),
                Minimum = 0,
                Maximum = 50,
                Value = 5
            };

            var lblTheme = new Label
            {
                Text = "表格主题:",
                Location = new Point(20, 55),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbTableTheme = new ComboBox
            {
                Location = new Point(100, 53),
                Size = new Size(150, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            groupBox.Controls.AddRange(new Control[] {
                lblPadding, lblTop, _nudCellPaddingTop, lblLeft, _nudCellPaddingLeft,
                lblRight, _nudCellPaddingRight, lblBottom, _nudCellPaddingBottom,
                lblTheme, _cmbTableTheme
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建颜色组
        /// </summary>
        private void CreateColorGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "背景颜色",
                Location = new Point(20, 240),
                Size = new Size(550, 100),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            var lblHeader = new Label
            {
                Text = "表头背景:",
                Location = new Point(20, 25),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _btnHeaderBackColor = new Button
            {
                Location = new Point(100, 23),
                Size = new Size(50, 25),
                BackColor = _headerBackColor,
                FlatStyle = FlatStyle.Flat
            };
            _btnHeaderBackColor.Click += (s, e) => SelectColor(ref _headerBackColor, _btnHeaderBackColor);

            var lblData = new Label
            {
                Text = "数据背景:",
                Location = new Point(170, 25),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _btnDataBackColor = new Button
            {
                Location = new Point(250, 23),
                Size = new Size(50, 25),
                BackColor = _dataBackColor,
                FlatStyle = FlatStyle.Flat
            };
            _btnDataBackColor.Click += (s, e) => SelectColor(ref _dataBackColor, _btnDataBackColor);

            var lblAlternate = new Label
            {
                Text = "交替行背景:",
                Location = new Point(320, 25),
                Size = new Size(80, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _btnAlternateBackColor = new Button
            {
                Location = new Point(410, 23),
                Size = new Size(50, 25),
                BackColor = _alternateBackColor,
                FlatStyle = FlatStyle.Flat
            };
            _btnAlternateBackColor.Click += (s, e) => SelectColor(ref _alternateBackColor, _btnAlternateBackColor);

            groupBox.Controls.AddRange(new Control[] {
                lblHeader, _btnHeaderBackColor, lblData, _btnDataBackColor, lblAlternate, _btnAlternateBackColor
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建字体组
        /// </summary>
        private void CreateFontGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "字体设置",
                Location = new Point(20, 350),
                Size = new Size(550, 100),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 表头字体
            var lblHeaderFont = new Label
            {
                Text = "表头字体:",
                Location = new Point(20, 25),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbHeaderFontStyle = new ComboBox
            {
                Location = new Point(100, 23),
                Size = new Size(80, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 8F)
            };

            _nudHeaderFontSize = new NumericUpDown
            {
                Location = new Point(190, 23),
                Size = new Size(50, 23),
                Minimum = 8,
                Maximum = 36,
                Value = 12
            };

            _btnHeaderFontColor = new Button
            {
                Location = new Point(250, 23),
                Size = new Size(30, 23),
                BackColor = _headerFontColor,
                FlatStyle = FlatStyle.Flat
            };
            _btnHeaderFontColor.Click += (s, e) => SelectColor(ref _headerFontColor, _btnHeaderFontColor);

            // 数据字体
            var lblDataFont = new Label
            {
                Text = "数据字体:",
                Location = new Point(300, 25),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbDataFontStyle = new ComboBox
            {
                Location = new Point(380, 23),
                Size = new Size(80, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 8F)
            };

            _nudDataFontSize = new NumericUpDown
            {
                Location = new Point(470, 23),
                Size = new Size(50, 23),
                Minimum = 8,
                Maximum = 36,
                Value = 10
            };

            _btnDataFontColor = new Button
            {
                Location = new Point(530, 23),
                Size = new Size(30, 23),
                BackColor = _dataFontColor,
                FlatStyle = FlatStyle.Flat
            };
            _btnDataFontColor.Click += (s, e) => SelectColor(ref _dataFontColor, _btnDataFontColor);

            groupBox.Controls.AddRange(new Control[] {
                lblHeaderFont, _cmbHeaderFontStyle, _nudHeaderFontSize, _btnHeaderFontColor,
                lblDataFont, _cmbDataFontStyle, _nudDataFontSize, _btnDataFontColor
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建选项组
        /// </summary>
        private void CreateOptionsGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "表格选项",
                Location = new Point(20, 460),
                Size = new Size(550, 80),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            _chkHeaderRow = new CheckBox { Text = "表头行", Location = new Point(20, 25), Size = new Size(70, 20) };
            _chkTotalRow = new CheckBox { Text = "汇总行", Location = new Point(100, 25), Size = new Size(70, 20) };
            _chkFirstColumn = new CheckBox { Text = "首列", Location = new Point(180, 25), Size = new Size(60, 20) };
            _chkLastColumn = new CheckBox { Text = "末列", Location = new Point(250, 25), Size = new Size(60, 20) };
            _chkAlternateRows = new CheckBox { Text = "交替行", Location = new Point(320, 25), Size = new Size(70, 20) };

            _btnPreview = new Button
            {
                Text = "预览",
                Location = new Point(450, 22),
                Size = new Size(60, 25),
                Font = new Font("Microsoft YaHei UI", 8F)
            };
            _btnPreview.Click += BtnPreview_Click;

            groupBox.Controls.AddRange(new Control[] {
                _chkHeaderRow, _chkTotalRow, _chkFirstColumn, _chkLastColumn, _chkAlternateRows, _btnPreview
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建操作按钮
        /// </summary>
        private void CreateActionButtons()
        {
            _btnOK = new Button
            {
                Text = "确定",
                Location = new Point(390, 560),
                Size = new Size(60, 30),
                DialogResult = DialogResult.OK,
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            _btnOK.Click += BtnOK_Click;

            _btnCancel = new Button
            {
                Text = "取消",
                Location = new Point(460, 560),
                Size = new Size(60, 30),
                DialogResult = DialogResult.Cancel,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            _btnReset = new Button
            {
                Text = "重置",
                Location = new Point(530, 560),
                Size = new Size(50, 30),
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            _btnReset.Click += BtnReset_Click;

            this.Controls.AddRange(new Control[] { _btnOK, _btnCancel, _btnReset });
        }

        /// <summary>
        /// 加载默认值
        /// </summary>
        private void LoadDefaultValues()
        {
            // 边框样式
            var borderStyles = new string[] { "实线", "虚线", "点线", "双线", "粗线" };
            _cmbBorderStyle?.Items.AddRange(borderStyles);
            if (_cmbBorderStyle != null)
                _cmbBorderStyle.SelectedItem = "实线";

            // 表格主题
            var themes = new string[] { "默认", "简洁", "现代", "经典", "彩色", "专业" };
            _cmbTableTheme?.Items.AddRange(themes);
            if (_cmbTableTheme != null)
                _cmbTableTheme.SelectedItem = "默认";

            // 字体样式
            var fontStyles = new string[] { "常规", "粗体", "斜体" };
            _cmbHeaderFontStyle?.Items.AddRange(fontStyles);
            _cmbDataFontStyle?.Items.AddRange(fontStyles);
            if (_cmbHeaderFontStyle != null)
                _cmbHeaderFontStyle.SelectedItem = "粗体";
            if (_cmbDataFontStyle != null)
                _cmbDataFontStyle.SelectedItem = "常规";
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 选择颜色
        /// </summary>
        /// <param name="colorField">颜色字段</param>
        /// <param name="button">颜色按钮</param>
        private void SelectColor(ref Color colorField, Button? button)
        {
            if (button == null) return;

            using var colorDialog = new ColorDialog
            {
                Color = colorField,
                FullOpen = true
            };

            if (colorDialog.ShowDialog(this) == DialogResult.OK)
            {
                colorField = colorDialog.Color;
                button.BackColor = colorField;
            }
        }

        #endregion

        #region 事件处理方法

        /// <summary>
        /// 预览按钮点击事件
        /// </summary>
        private void BtnPreview_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("预览功能显示当前表格样式的效果。\n\n" +
                           "在实际应用中，这里会显示表格样式预览。", "样式预览",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void BtnOK_Click(object? sender, EventArgs e)
        {
            try
            {
                // 收集设置
                Settings = new TableStyleSettings
                {
                    ShowBorder = _chkShowBorder?.Checked ?? true,
                    BorderStyle = _cmbBorderStyle?.SelectedItem?.ToString() ?? "实线",
                    BorderWidth = (int)(_nudBorderWidth?.Value ?? 1),
                    BorderColor = _borderColor,
                    CellPaddingTop = (int)(_nudCellPaddingTop?.Value ?? 5),
                    CellPaddingLeft = (int)(_nudCellPaddingLeft?.Value ?? 5),
                    CellPaddingRight = (int)(_nudCellPaddingRight?.Value ?? 5),
                    CellPaddingBottom = (int)(_nudCellPaddingBottom?.Value ?? 5),
                    TableTheme = _cmbTableTheme?.SelectedItem?.ToString() ?? "默认",
                    HeaderBackColor = _headerBackColor,
                    DataBackColor = _dataBackColor,
                    AlternateBackColor = _alternateBackColor,
                    HeaderFontStyle = _cmbHeaderFontStyle?.SelectedItem?.ToString() ?? "粗体",
                    HeaderFontSize = (int)(_nudHeaderFontSize?.Value ?? 12),
                    HeaderFontColor = _headerFontColor,
                    DataFontStyle = _cmbDataFontStyle?.SelectedItem?.ToString() ?? "常规",
                    DataFontSize = (int)(_nudDataFontSize?.Value ?? 10),
                    DataFontColor = _dataFontColor,
                    HasHeaderRow = _chkHeaderRow?.Checked ?? false,
                    HasTotalRow = _chkTotalRow?.Checked ?? false,
                    HasFirstColumn = _chkFirstColumn?.Checked ?? false,
                    HasLastColumn = _chkLastColumn?.Checked ?? false,
                    HasAlternateRows = _chkAlternateRows?.Checked ?? false
                };

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存设置时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 重置按钮点击事件
        /// </summary>
        private void BtnReset_Click(object? sender, EventArgs e)
        {
            LoadDefaultValues();
            
            // 重置数值控件
            if (_nudBorderWidth != null) _nudBorderWidth.Value = 1;
            if (_nudCellPaddingTop != null) _nudCellPaddingTop.Value = 5;
            if (_nudCellPaddingLeft != null) _nudCellPaddingLeft.Value = 5;
            if (_nudCellPaddingRight != null) _nudCellPaddingRight.Value = 5;
            if (_nudCellPaddingBottom != null) _nudCellPaddingBottom.Value = 5;
            if (_nudHeaderFontSize != null) _nudHeaderFontSize.Value = 12;
            if (_nudDataFontSize != null) _nudDataFontSize.Value = 10;
            
            // 重置复选框
            if (_chkShowBorder != null) _chkShowBorder.Checked = true;
            if (_chkHeaderRow != null) _chkHeaderRow.Checked = false;
            if (_chkTotalRow != null) _chkTotalRow.Checked = false;
            if (_chkFirstColumn != null) _chkFirstColumn.Checked = false;
            if (_chkLastColumn != null) _chkLastColumn.Checked = false;
            if (_chkAlternateRows != null) _chkAlternateRows.Checked = false;
            
            // 重置颜色
            _borderColor = Color.Black;
            _headerBackColor = Color.LightGray;
            _dataBackColor = Color.White;
            _alternateBackColor = Color.LightBlue;
            _headerFontColor = Color.Black;
            _dataFontColor = Color.Black;
            
            if (_btnBorderColor != null) _btnBorderColor.BackColor = _borderColor;
            if (_btnHeaderBackColor != null) _btnHeaderBackColor.BackColor = _headerBackColor;
            if (_btnDataBackColor != null) _btnDataBackColor.BackColor = _dataBackColor;
            if (_btnAlternateBackColor != null) _btnAlternateBackColor.BackColor = _alternateBackColor;
            if (_btnHeaderFontColor != null) _btnHeaderFontColor.BackColor = _headerFontColor;
            if (_btnDataFontColor != null) _btnDataFontColor.BackColor = _dataFontColor;
        }

        #endregion
    }

    /// <summary>
    /// 表格样式设置类
    /// </summary>
    public class TableStyleSettings
    {
        public bool ShowBorder { get; set; } = true;
        public string BorderStyle { get; set; } = "实线";
        public int BorderWidth { get; set; } = 1;
        public Color BorderColor { get; set; } = Color.Black;
        public int CellPaddingTop { get; set; } = 5;
        public int CellPaddingLeft { get; set; } = 5;
        public int CellPaddingRight { get; set; } = 5;
        public int CellPaddingBottom { get; set; } = 5;
        public string TableTheme { get; set; } = "默认";
        public Color HeaderBackColor { get; set; } = Color.LightGray;
        public Color DataBackColor { get; set; } = Color.White;
        public Color AlternateBackColor { get; set; } = Color.LightBlue;
        public string HeaderFontStyle { get; set; } = "粗体";
        public int HeaderFontSize { get; set; } = 12;
        public Color HeaderFontColor { get; set; } = Color.Black;
        public string DataFontStyle { get; set; } = "常规";
        public int DataFontSize { get; set; } = 10;
        public Color DataFontColor { get; set; } = Color.Black;
        public bool HasHeaderRow { get; set; } = false;
        public bool HasTotalRow { get; set; } = false;
        public bool HasFirstColumn { get; set; } = false;
        public bool HasLastColumn { get; set; } = false;
        public bool HasAlternateRows { get; set; } = false;
    }
}
